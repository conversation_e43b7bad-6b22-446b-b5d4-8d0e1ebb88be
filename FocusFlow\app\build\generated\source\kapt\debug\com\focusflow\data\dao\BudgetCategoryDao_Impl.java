package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.model.BudgetCategory;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class BudgetCategoryDao_Impl implements BudgetCategoryDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<BudgetCategory> __insertionAdapterOfBudgetCategory;

  private final EntityDeletionOrUpdateAdapter<BudgetCategory> __deletionAdapterOfBudgetCategory;

  private final EntityDeletionOrUpdateAdapter<BudgetCategory> __updateAdapterOfBudgetCategory;

  private final SharedSQLiteStatement __preparedStmtOfUpdateSpentAmount;

  public BudgetCategoryDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfBudgetCategory = new EntityInsertionAdapter<BudgetCategory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `budget_categories` (`id`,`name`,`allocatedAmount`,`spentAmount`,`budgetPeriod`,`budgetYear`,`budgetMonth`,`budgetWeek`,`isActive`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BudgetCategory entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        statement.bindDouble(3, entity.getAllocatedAmount());
        statement.bindDouble(4, entity.getSpentAmount());
        if (entity.getBudgetPeriod() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getBudgetPeriod());
        }
        statement.bindLong(6, entity.getBudgetYear());
        if (entity.getBudgetMonth() == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, entity.getBudgetMonth());
        }
        if (entity.getBudgetWeek() == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, entity.getBudgetWeek());
        }
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(9, _tmp);
      }
    };
    this.__deletionAdapterOfBudgetCategory = new EntityDeletionOrUpdateAdapter<BudgetCategory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `budget_categories` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BudgetCategory entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfBudgetCategory = new EntityDeletionOrUpdateAdapter<BudgetCategory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `budget_categories` SET `id` = ?,`name` = ?,`allocatedAmount` = ?,`spentAmount` = ?,`budgetPeriod` = ?,`budgetYear` = ?,`budgetMonth` = ?,`budgetWeek` = ?,`isActive` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BudgetCategory entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        statement.bindDouble(3, entity.getAllocatedAmount());
        statement.bindDouble(4, entity.getSpentAmount());
        if (entity.getBudgetPeriod() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getBudgetPeriod());
        }
        statement.bindLong(6, entity.getBudgetYear());
        if (entity.getBudgetMonth() == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, entity.getBudgetMonth());
        }
        if (entity.getBudgetWeek() == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, entity.getBudgetWeek());
        }
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(9, _tmp);
        statement.bindLong(10, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateSpentAmount = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE budget_categories SET spentAmount = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertBudgetCategory(final BudgetCategory budgetCategory,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfBudgetCategory.insertAndReturnId(budgetCategory);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteBudgetCategory(final BudgetCategory budgetCategory,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfBudgetCategory.handle(budgetCategory);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateBudgetCategory(final BudgetCategory budgetCategory,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfBudgetCategory.handle(budgetCategory);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSpentAmount(final long categoryId, final double spentAmount,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateSpentAmount.acquire();
        int _argIndex = 1;
        _stmt.bindDouble(_argIndex, spentAmount);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, categoryId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateSpentAmount.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<BudgetCategory>> getMonthlyBudgetCategories(final String period, final int year,
      final int month) {
    final String _sql = "SELECT * FROM budget_categories WHERE isActive = 1 AND budgetPeriod = ? AND budgetYear = ? AND budgetMonth = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_categories"}, new Callable<List<BudgetCategory>>() {
      @Override
      @NonNull
      public List<BudgetCategory> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfAllocatedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "allocatedAmount");
          final int _cursorIndexOfSpentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "spentAmount");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<BudgetCategory> _result = new ArrayList<BudgetCategory>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetCategory _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final double _tmpAllocatedAmount;
            _tmpAllocatedAmount = _cursor.getDouble(_cursorIndexOfAllocatedAmount);
            final double _tmpSpentAmount;
            _tmpSpentAmount = _cursor.getDouble(_cursorIndexOfSpentAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item = new BudgetCategory(_tmpId,_tmpName,_tmpAllocatedAmount,_tmpSpentAmount,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BudgetCategory>> getWeeklyBudgetCategories(final String period, final int year,
      final int week) {
    final String _sql = "SELECT * FROM budget_categories WHERE isActive = 1 AND budgetPeriod = ? AND budgetYear = ? AND budgetWeek = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, week);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_categories"}, new Callable<List<BudgetCategory>>() {
      @Override
      @NonNull
      public List<BudgetCategory> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfAllocatedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "allocatedAmount");
          final int _cursorIndexOfSpentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "spentAmount");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<BudgetCategory> _result = new ArrayList<BudgetCategory>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetCategory _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final double _tmpAllocatedAmount;
            _tmpAllocatedAmount = _cursor.getDouble(_cursorIndexOfAllocatedAmount);
            final double _tmpSpentAmount;
            _tmpSpentAmount = _cursor.getDouble(_cursorIndexOfSpentAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item = new BudgetCategory(_tmpId,_tmpName,_tmpAllocatedAmount,_tmpSpentAmount,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BudgetCategory>> getBudgetCategoriesByPeriod(final String period) {
    final String _sql = "SELECT * FROM budget_categories WHERE isActive = 1 AND budgetPeriod = ? ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_categories"}, new Callable<List<BudgetCategory>>() {
      @Override
      @NonNull
      public List<BudgetCategory> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfAllocatedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "allocatedAmount");
          final int _cursorIndexOfSpentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "spentAmount");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<BudgetCategory> _result = new ArrayList<BudgetCategory>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetCategory _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final double _tmpAllocatedAmount;
            _tmpAllocatedAmount = _cursor.getDouble(_cursorIndexOfAllocatedAmount);
            final double _tmpSpentAmount;
            _tmpSpentAmount = _cursor.getDouble(_cursorIndexOfSpentAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item = new BudgetCategory(_tmpId,_tmpName,_tmpAllocatedAmount,_tmpSpentAmount,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getTotalMonthlyBudget(final String period, final int year, final int month,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(allocatedAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = ? AND budgetYear = ? AND budgetMonth = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalWeeklyBudget(final String period, final int year, final int week,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(allocatedAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = ? AND budgetYear = ? AND budgetWeek = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, week);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalMonthlySpent(final String period, final int year, final int month,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(spentAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = ? AND budgetYear = ? AND budgetMonth = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, month);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalWeeklySpent(final String period, final int year, final int week,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(spentAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = ? AND budgetYear = ? AND budgetWeek = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    _statement.bindLong(_argIndex, week);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<BudgetCategory>> getAllBudgetCategories() {
    final String _sql = "SELECT `budget_categories`.`id` AS `id`, `budget_categories`.`name` AS `name`, `budget_categories`.`allocatedAmount` AS `allocatedAmount`, `budget_categories`.`spentAmount` AS `spentAmount`, `budget_categories`.`budgetPeriod` AS `budgetPeriod`, `budget_categories`.`budgetYear` AS `budgetYear`, `budget_categories`.`budgetMonth` AS `budgetMonth`, `budget_categories`.`budgetWeek` AS `budgetWeek`, `budget_categories`.`isActive` AS `isActive` FROM budget_categories WHERE isActive = 1 ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_categories"}, new Callable<List<BudgetCategory>>() {
      @Override
      @NonNull
      public List<BudgetCategory> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfName = 1;
          final int _cursorIndexOfAllocatedAmount = 2;
          final int _cursorIndexOfSpentAmount = 3;
          final int _cursorIndexOfBudgetPeriod = 4;
          final int _cursorIndexOfBudgetYear = 5;
          final int _cursorIndexOfBudgetMonth = 6;
          final int _cursorIndexOfBudgetWeek = 7;
          final int _cursorIndexOfIsActive = 8;
          final List<BudgetCategory> _result = new ArrayList<BudgetCategory>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetCategory _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final double _tmpAllocatedAmount;
            _tmpAllocatedAmount = _cursor.getDouble(_cursorIndexOfAllocatedAmount);
            final double _tmpSpentAmount;
            _tmpSpentAmount = _cursor.getDouble(_cursorIndexOfSpentAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _item = new BudgetCategory(_tmpId,_tmpName,_tmpAllocatedAmount,_tmpSpentAmount,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getBudgetCategoryByName(final String name,
      final Continuation<? super BudgetCategory> $completion) {
    final String _sql = "SELECT * FROM budget_categories WHERE isActive = 1 AND name = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (name == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, name);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<BudgetCategory>() {
      @Override
      @Nullable
      public BudgetCategory call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfAllocatedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "allocatedAmount");
          final int _cursorIndexOfSpentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "spentAmount");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final BudgetCategory _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final double _tmpAllocatedAmount;
            _tmpAllocatedAmount = _cursor.getDouble(_cursorIndexOfAllocatedAmount);
            final double _tmpSpentAmount;
            _tmpSpentAmount = _cursor.getDouble(_cursorIndexOfSpentAmount);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            _result = new BudgetCategory(_tmpId,_tmpName,_tmpAllocatedAmount,_tmpSpentAmount,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpIsActive);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalBudgetForPeriod(final String period,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(allocatedAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalSpentForPeriod(final String period,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(spentAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
