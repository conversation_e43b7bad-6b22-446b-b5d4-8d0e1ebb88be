<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res"><file name="ic_launcher_foreground" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\mipmap-hdpi\ic_launcher.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\mipmap-hdpi\ic_launcher_round.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\mipmap-mdpi\ic_launcher.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\mipmap-mdpi\ic_launcher_round.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\mipmap-xhdpi\ic_launcher.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\mipmap-xhdpi\ic_launcher_round.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\mipmap-xxhdpi\ic_launcher.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\mipmap-xxxhdpi\ic_launcher.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="focus_blue">#FF2196F3</color><color name="focus_blue_dark">#FF1976D2</color><color name="focus_green">#FF4CAF50</color><color name="focus_orange">#FFFF9800</color><color name="ic_launcher_background">#FF2196F3</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="gray_50">#FFFAFAFA</color><color name="gray_100">#FFF5F5F5</color><color name="gray_200">#FFEEEEEE</color><color name="gray_300">#FFE0E0E0</color><color name="gray_400">#FFBDBDBD</color><color name="gray_500">#FF9E9E9E</color><color name="gray_600">#FF757575</color><color name="gray_700">#FF616161</color><color name="gray_800">#FF424242</color><color name="gray_900">#FF212121</color></file><file path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">FocusFlow</string><string name="welcome_title">Welcome to FocusFlow</string><string name="welcome_subtitle">Your ADHD-friendly companion for better focus and productivity</string><string name="get_started">Get Started</string><string name="notification_channel_name">FocusFlow Notifications</string><string name="notification_channel_description">Notifications for tasks, reminders and focus sessions</string><string name="default_notification_title">FocusFlow Reminder</string><string name="default_notification_message">Time to check your tasks!</string></file><file path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.FocusFlow" parent="Theme.AppCompat.DayNight">
        
        <item name="colorPrimary">@color/focus_blue</item>
        <item name="colorPrimaryDark">@color/focus_blue_dark</item>
        <item name="colorAccent">@color/focus_green</item>
        
        <item name="android:statusBarColor">@color/focus_blue_dark</item>
        
        <item name="android:windowBackground">@color/white</item>
    </style><style name="Theme.FocusFlow.NoActionBar" parent="Theme.FocusFlow">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build\generated\res\resValues\release"/></dataSet><mergedItems/></merger>