<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DashboardScreen.kt"
            line="128"
            column="32"
            startOffset="3676"
            endLine="128"
            endColumn="66"
            endOffset="3710"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DashboardScreen.kt"
            line="178"
            column="55"
            startOffset="5394"
            endLine="178"
            endColumn="87"
            endOffset="5426"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DashboardScreen.kt"
            line="190"
            column="57"
            startOffset="5984"
            endLine="190"
            endColumn="91"
            endOffset="6018"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="225"
            column="28"
            startOffset="7512"
            endLine="225"
            endColumn="57"
            endOffset="7541"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="300"
            column="36"
            startOffset="10004"
            endLine="300"
            endColumn="84"
            endOffset="10052"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="313"
            column="36"
            startOffset="10591"
            endLine="313"
            endColumn="81"
            endOffset="10636"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="335"
            column="35"
            startOffset="11490"
            endLine="335"
            endColumn="75"
            endOffset="11530"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="369"
            column="49"
            startOffset="12887"
            endLine="369"
            endColumn="97"
            endOffset="12935"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="575"
            column="49"
            startOffset="20770"
            endLine="575"
            endColumn="97"
            endOffset="20818"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="597"
            column="53"
            startOffset="21770"
            endLine="597"
            endColumn="101"
            endOffset="21818"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="602"
            column="53"
            startOffset="21995"
            endLine="602"
            endColumn="101"
            endOffset="22043"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/utils/ErrorHandling.kt"
            line="198"
            column="31"
            startOffset="6164"
            endLine="198"
            endColumn="60"
            endOffset="6193"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
            line="70"
            column="54"
            startOffset="2233"
            endLine="70"
            endColumn="83"
            endOffset="2262"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
            line="282"
            column="56"
            startOffset="10217"
            endLine="282"
            endColumn="110"
            endOffset="10271"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
            line="284"
            column="43"
            startOffset="10358"
            endLine="284"
            endColumn="81"
            endOffset="10396"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/OtherScreens.kt"
            line="169"
            column="28"
            startOffset="5562"
            endLine="169"
            endColumn="61"
            endOffset="5595"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/OtherScreens.kt"
            line="220"
            column="40"
            startOffset="7461"
            endLine="220"
            endColumn="68"
            endOffset="7489"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/OtherScreens.kt"
            line="279"
            column="32"
            startOffset="9384"
            endLine="279"
            endColumn="69"
            endOffset="9421"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="180"
            column="35"
            startOffset="5944"
            endLine="180"
            endColumn="79"
            endOffset="5988"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="254"
            column="24"
            startOffset="8573"
            endLine="254"
            endColumn="53"
            endOffset="8602"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="326"
            column="36"
            startOffset="10942"
            endLine="326"
            endColumn="85"
            endOffset="10991"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="326"
            column="93"
            startOffset="10999"
            endLine="326"
            endColumn="146"
            endOffset="11052"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="330"
            column="35"
            startOffset="11201"
            endLine="330"
            endColumn="79"
            endOffset="11245"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="358"
            column="47"
            startOffset="12459"
            endLine="358"
            endColumn="80"
            endOffset="12492"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="16"
            column="9"
            startOffset="310"
            endLine="16"
            endColumn="21"
            endOffset="322"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="42"
            column="13"
            startOffset="1856"
            endLine="42"
            endColumn="45"
            endOffset="1888"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of Gradle than 8.12 is available: 8.14.2">
        <fix-replace
            description="Update to 8.14.2"
            oldString="8.12"
            replacement="8.14.2"
            priority="0"/>
        <location
            file="$HOME/Downloads/Understanding Content in pasted_content (1)/FocusFlow/gradle/wrapper/gradle-wrapper.properties"
            line="3"
            column="17"
            startOffset="81"
            endLine="3"
            endColumn="79"
            endOffset="143"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of `compileSdkVersion` than 34 is available: 35">
        <fix-replace
            description="Set compileSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="11"
            column="5"
            startOffset="211"
            endLine="11"
            endColumn="18"
            endOffset="224"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.12.0"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="115"
            column="20"
            startOffset="2859"
            endLine="115"
            endColumn="51"
            endOffset="2890"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="116"
            column="20"
            startOffset="2910"
            endLine="116"
            endColumn="68"
            endOffset="2958"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.2"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="117"
            column="20"
            startOffset="2978"
            endLine="117"
            endColumn="62"
            endOffset="3020"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2023.10.01 is available: 2025.06.00">
        <fix-replace
            description="Change to 2025.06.00"
            family="Update versions"
            oldString="2023.10.01"
            replacement="2025.06.00"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="120"
            column="20"
            startOffset="3064"
            endLine="120"
            endColumn="71"
            endOffset="3115"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-compose than 2.7.5 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.5"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="129"
            column="20"
            startOffset="3511"
            endLine="129"
            endColumn="66"
            endOffset="3557"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="132"
            column="20"
            startOffset="3599"
            endLine="132"
            endColumn="74"
            endOffset="3653"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-compose than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="133"
            column="20"
            startOffset="3673"
            endLine="133"
            endColumn="72"
            endOffset="3725"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.hilt:hilt-navigation-compose than 1.1.0 is available: 1.2.0">
        <fix-replace
            description="Change to 1.2.0"
            family="Update versions"
            oldString="1.1.0"
            replacement="1.2.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="137"
            column="20"
            startOffset="3819"
            endLine="137"
            endColumn="65"
            endOffset="3864"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="141"
            column="20"
            startOffset="3949"
            endLine="141"
            endColumn="54"
            endOffset="3983"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="142"
            column="20"
            startOffset="4003"
            endLine="142"
            endColumn="50"
            endOffset="4033"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="143"
            column="10"
            startOffset="4043"
            endLine="143"
            endColumn="45"
            endOffset="4078"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-beta01">
        <fix-replace
            description="Change to 1.1.0-beta01"
            family="Update versions"
            oldString="1.1.0-alpha06"
            replacement="1.1.0-beta01"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="163"
            column="20"
            startOffset="4718"
            endLine="163"
            endColumn="69"
            endOffset="4767"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.1">
        <fix-replace
            description="Change to 2.10.1"
            family="Update versions"
            oldString="2.9.0"
            replacement="2.10.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="166"
            column="20"
            startOffset="4833"
            endLine="166"
            endColumn="58"
            endOffset="4871"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.hilt:hilt-work than 1.1.0 is available: 1.2.0">
        <fix-replace
            description="Change to 1.2.0"
            family="Update versions"
            oldString="1.1.0"
            replacement="1.2.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="167"
            column="20"
            startOffset="4891"
            endLine="167"
            endColumn="51"
            endOffset="4922"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.android.tools:desugar_jdk_libs than 2.0.4 is available: 2.1.5">
        <fix-replace
            description="Change to 2.1.5"
            family="Update versions"
            oldString="2.0.4"
            replacement="2.1.5"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="181"
            column="27"
            startOffset="5468"
            endLine="181"
            endColumn="69"
            endOffset="5510"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test:core than 1.5.0 is available: 1.6.1">
        <fix-replace
            description="Change to 1.6.1"
            family="Update versions"
            oldString="1.5.0"
            replacement="1.6.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="185"
            column="24"
            startOffset="5598"
            endLine="185"
            endColumn="50"
            endOffset="5624"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="186"
            column="24"
            startOffset="5648"
            endLine="186"
            endColumn="55"
            endOffset="5679"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-testing than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="187"
            column="24"
            startOffset="5703"
            endLine="187"
            endColumn="58"
            endOffset="5737"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="193"
            column="31"
            startOffset="5990"
            endLine="193"
            endColumn="62"
            endOffset="6021"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="194"
            column="31"
            startOffset="6052"
            endLine="194"
            endColumn="75"
            endOffset="6096"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2023.10.01 is available: 2025.06.00">
        <fix-replace
            description="Change to 2025.06.00"
            family="Update versions"
            oldString="2023.10.01"
            replacement="2025.06.00"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="195"
            column="31"
            startOffset="6127"
            endLine="195"
            endColumn="82"
            endOffset="6178"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-testing than 2.7.5 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.5"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="197"
            column="31"
            startOffset="6276"
            endLine="197"
            endColumn="77"
            endOffset="6322"/>
    </incident>

    <incident
        id="MutableCollectionMutableState"
        severity="warning"
        message="Creating a MutableState object with a mutable collection type">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="465"
            column="42"
            startOffset="15895"
            endLine="465"
            endColumn="56"
            endOffset="15909"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="44"
            column="13"
            startOffset="1952"
            endLine="44"
            endColumn="52"
            endOffset="1991"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="57"
            column="13"
            startOffset="2500"
            endLine="57"
            endColumn="52"
            endOffset="2539"/>
    </incident>

    <incident
        id="KaptUsageInsteadOfKsp"
        severity="warning"
        message="This library supports using KSP instead of kapt, which greatly improves performance. Learn more: https://developer.android.com/studio/build/migrate-to-ksp">
        <show-url
            description="Learn about how to enable KSP and use the KSP processor for this dependency instead"
            url="https://developer.android.com/studio/build/migrate-to-ksp"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="143"
            column="5"
            startOffset="4038"
            endLine="143"
            endColumn="45"
            endOffset="4078"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableIntStateOf"
            replacement="androidx.compose.runtime.mutableIntStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
                startOffset="820"
                endOffset="834"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
            line="26"
            column="37"
            startOffset="820"
            endLine="26"
            endColumn="51"
            endOffset="834"/>
    </incident>

</incidents>
