package com.focusflow.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.ui.viewmodel.DashboardViewModel

@Composable
fun DashboardScreen(
    viewModel: DashboardViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Welcome message
            Text(
                text = "Welcome back!",
                style = MaterialTheme.typography.h4,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }

        item {
            // Safe-to-Spend Widget
            SafeToSpendWidget(
                safeToSpend = uiState.safeToSpend,
                period = uiState.budgetPeriod,
                isLoading = uiState.isLoading
            )
        }

        item {
            // Credit Card Summary
            CreditCardSummaryCard(
                totalDebt = uiState.totalDebt,
                nextPayment = uiState.nextPayment
            )
        }

        item {
            // Today's Tasks
            TodaysTasksCard()
        }

        item {
            // Habit Streak Indicators
            HabitStreakCard()
        }

        item {
            // Motivational Quote
            MotivationalQuoteCard()
        }

        item {
            // Virtual Pet Widget
            VirtualPetWidget()
        }

        item {
            // Progress & Achievements
            ProgressAchievementsCard()
        }
    }

    // Floating Action Button for Quick Add
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.BottomEnd
    ) {
        FloatingActionButton(
            onClick = { /* TODO: Implement quick add */ },
            modifier = Modifier.padding(16.dp),
            backgroundColor = MaterialTheme.colors.primary
        ) {
            Icon(Icons.Default.Add, contentDescription = "Quick Add")
        }
    }

    // Error handling
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // TODO: Show snackbar
        }
    }
}

@Composable
fun SafeToSpendWidget(
    safeToSpend: Double,
    period: String,
    isLoading: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Safe to Spend",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(48.dp),
                    color = MaterialTheme.colors.primary
                )
            } else {
                Text(
                    text = "$${String.format("%.2f", safeToSpend)}",
                    style = MaterialTheme.typography.h3,
                    fontWeight = FontWeight.Bold,
                    color = when {
                        safeToSpend > 100 -> Color(0xFF4CAF50) // Green
                        safeToSpend > 0 -> Color(0xFFFF9800) // Orange
                        else -> Color(0xFFF44336) // Red
                    }
                )
            }
            
            Text(
                text = "This ${period.replaceFirstChar { it.lowercase() }}",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun CreditCardSummaryCard(
    totalDebt: Double,
    nextPayment: Double
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Credit Cards",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "Total Debt",
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = if (totalDebt > 0) "$${String.format("%.2f", totalDebt)}" else "No debt",
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Bold
                    )
                }
                Column(horizontalAlignment = Alignment.End) {
                    Text(
                        text = "Next Payment",
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = if (nextPayment > 0) "$${String.format("%.2f", nextPayment)}" else "None due",
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Bold,
                        color = if (nextPayment > 0) Color(0xFFFF9800) else MaterialTheme.colors.onSurface
                    )
                }
            }
        }
    }
}

@Composable
fun TodaysTasksCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Today's Tasks",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            // Sample tasks
            TaskItem("Review monthly budget", false)
            TaskItem("Pay credit card bill", true)
            TaskItem("Log morning medication", true)
        }
    }
}

@Composable
fun TaskItem(task: String, completed: Boolean) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = completed,
            onCheckedChange = { /* TODO: Implement task completion */ }
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = task,
            style = MaterialTheme.typography.body2,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
fun HabitStreakCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Habit Streaks",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                HabitStreakItem("💊", "Medication", 7)
                HabitStreakItem("😴", "Sleep", 5)
                HabitStreakItem("🏃", "Exercise", 3)
                HabitStreakItem("📝", "Logging", 12)
            }
        }
    }
}

@Composable
fun HabitStreakItem(emoji: String, habit: String, streak: Int) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = emoji,
            fontSize = 24.sp
        )
        Text(
            text = "$streak",
            style = MaterialTheme.typography.h6,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colors.primary
        )
        Text(
            text = habit,
            style = MaterialTheme.typography.caption,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun MotivationalQuoteCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "💪",
                fontSize = 32.sp
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "You're building great habits! Every small step counts towards your financial freedom.",
                style = MaterialTheme.typography.body1,
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
fun VirtualPetWidget() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Pet avatar
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .background(
                        MaterialTheme.colors.primary.copy(alpha = 0.1f),
                        RoundedCornerShape(30.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "🐱",
                    style = MaterialTheme.typography.h4
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "Buddy",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Level 1 • Happy",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )

                Spacer(modifier = Modifier.height(4.dp))

                // Happiness bar
                LinearProgressIndicator(
                    progress = 0.85f,
                    modifier = Modifier.fillMaxWidth(),
                    color = Color(0xFF4CAF50)
                )
            }
        }
    }
}

@Composable
fun ProgressAchievementsCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Progress",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "Level 1",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.primary,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // XP Progress bar
            LinearProgressIndicator(
                progress = 0.3f,
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colors.primary
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "150 / 500 XP",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )

            Spacer(modifier = Modifier.height(12.dp))

            // Recent achievements
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                AchievementBadge("🎯", "First Step")
                AchievementBadge("📝", "Getting Started")
                AchievementBadge("🔒", "Locked", isLocked = true)
            }
        }
    }
}

@Composable
fun AchievementBadge(
    emoji: String,
    title: String,
    isLocked: Boolean = false
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .background(
                    if (isLocked) {
                        MaterialTheme.colors.onSurface.copy(alpha = 0.1f)
                    } else {
                        MaterialTheme.colors.primary.copy(alpha = 0.1f)
                    },
                    RoundedCornerShape(20.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (isLocked) "🔒" else emoji,
                style = MaterialTheme.typography.body1
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = title,
            style = MaterialTheme.typography.caption,
            textAlign = TextAlign.Center,
            color = if (isLocked) {
                MaterialTheme.colors.onSurface.copy(alpha = 0.5f)
            } else {
                MaterialTheme.colors.onSurface
            },
            maxLines = 1
        )
    }
}

