<variant
    name="release"
    useSupportLibraryVectorDrawables="true"
    package="com.focusflow"
    minSdkVersion="24"
    targetSdkVersion="34"
    shrinking="true"
    mergedManifest="build\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.10.1;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\05f1af11feb90bf7d67306d315722be3\transformed\desugar_jdk_libs_configuration-2.0.4-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\release\compileReleaseJavaWithJavac\classes;build\tmp\kotlin-classes\release;build\tmp\kapt3\classes\release;build\kotlinToolingMetadata;build\intermediates\hilt\component_classes\release;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.focusflow"
      generatedSourceFolders="build\generated\source\kapt\release;build\generated\ap_generated_sources\release\out"
      generatedResourceFolders="build\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\05f1af11feb90bf7d67306d315722be3\transformed\desugar_jdk_libs_configuration-2.0.4-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
