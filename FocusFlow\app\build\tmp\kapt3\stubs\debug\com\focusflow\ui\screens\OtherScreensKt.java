package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000j\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0006\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0000\u001a\u0012\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0007\u001a>\u0010\u0004\u001a\u00020\u00012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062&\u0010\u0007\u001a\"\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\n\u0012\u0006\u0012\u0004\u0018\u00010\n\u0012\u0004\u0012\u00020\u00010\bH\u0007\u001a\b\u0010\u000b\u001a\u00020\u0001H\u0007\u001a\b\u0010\f\u001a\u00020\u0001H\u0007\u001a\u001e\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u001a\u0012\u0010\u0011\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u0012H\u0007\u001a;\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u00152\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\b\b\u0002\u0010\u0017\u001a\u00020\u00182\u0011\u0010\u0019\u001a\r\u0012\u0004\u0012\u00020\u00010\u0006\u00a2\u0006\u0002\b\u001aH\u0007\u001a\b\u0010\u001b\u001a\u00020\u0001H\u0007\u001a\u0010\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u001eH\u0007\u001a<\u0010\u001f\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\n2\u0012\u0010 \u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010!2\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\b\b\u0002\u0010#\u001a\u00020\u0015H\u0007\u001a\u0016\u0010$\u001a\u00020\u00012\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u000f0&H\u0007\u001a\u0018\u0010\'\u001a\u00020\u00012\u0006\u0010(\u001a\u00020\t2\u0006\u0010)\u001a\u00020\nH\u0007\u001a\u001e\u0010*\u001a\u00020\u00012\u0006\u0010+\u001a\u00020\n2\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u001a\u001c\u0010,\u001a\u00020\u00012\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010!H\u0007\u001a\b\u0010.\u001a\u00020\u0001H\u0007\u001a\b\u0010/\u001a\u00020\u0001H\u0007\u001a\u0010\u00100\u001a\u00020\n2\u0006\u00101\u001a\u000202H\u0002\u00a8\u00063"}, d2 = {"AICoachScreen", "", "viewModel", "Lcom/focusflow/ui/viewmodel/AICoachViewModel;", "AddExpenseDialog", "onDismiss", "Lkotlin/Function0;", "onAddExpense", "Lkotlin/Function4;", "", "", "BudgetScreen", "DebtScreen", "ExpenseItem", "expense", "Lcom/focusflow/data/model/Expense;", "onDelete", "ExpensesScreen", "Lcom/focusflow/ui/viewmodel/ExpenseViewModel;", "FilterChip", "selected", "", "onClick", "modifier", "Landroidx/compose/ui/Modifier;", "content", "Landroidx/compose/runtime/Composable;", "HabitsScreen", "MessageBubble", "message", "Lcom/focusflow/ui/viewmodel/ChatMessage;", "MessageInputField", "onMessageChange", "Lkotlin/Function1;", "onSend", "enabled", "QuickCategoryOverview", "expenses", "", "SpendingSummaryCard", "totalSpent", "period", "SuggestedPromptCard", "prompt", "SuggestedPromptsSection", "onPromptSelected", "TasksScreen", "TypingIndicator", "formatDate", "dateTime", "Lkotlinx/datetime/LocalDateTime;", "app_debug"})
public final class OtherScreensKt {
    
    @androidx.compose.runtime.Composable
    public static final void ExpensesScreen(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.ExpenseViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void SpendingSummaryCard(double totalSpent, @org.jetbrains.annotations.NotNull
    java.lang.String period) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void QuickCategoryOverview(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.Expense> expenses) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ExpenseItem(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Expense expense, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDelete) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void AddExpenseDialog(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function4<? super java.lang.Double, ? super java.lang.String, ? super java.lang.String, ? super java.lang.String, kotlin.Unit> onAddExpense) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void FilterChip(boolean selected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    private static final java.lang.String formatDate(kotlinx.datetime.LocalDateTime dateTime) {
        return null;
    }
    
    @androidx.compose.runtime.Composable
    public static final void DebtScreen() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void BudgetScreen() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void HabitsScreen() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TasksScreen() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void AICoachScreen(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.AICoachViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void SuggestedPromptsSection(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onPromptSelected) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void SuggestedPromptCard(@org.jetbrains.annotations.NotNull
    java.lang.String prompt, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void MessageBubble(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.ChatMessage message) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TypingIndicator() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void MessageInputField(@org.jetbrains.annotations.NotNull
    java.lang.String message, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMessageChange, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onSend, boolean enabled) {
    }
}