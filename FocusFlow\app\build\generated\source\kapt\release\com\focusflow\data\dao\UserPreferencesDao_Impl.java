package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.model.UserPreferences;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class UserPreferencesDao_Impl implements UserPreferencesDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<UserPreferences> __insertionAdapterOfUserPreferences;

  private final EntityDeletionOrUpdateAdapter<UserPreferences> __updateAdapterOfUserPreferences;

  private final SharedSQLiteStatement __preparedStmtOfUpdateBudgetPeriod;

  private final SharedSQLiteStatement __preparedStmtOfUpdateNotificationsEnabled;

  private final SharedSQLiteStatement __preparedStmtOfUpdateDarkModeEnabled;

  private final SharedSQLiteStatement __preparedStmtOfUpdateFontSize;

  public UserPreferencesDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfUserPreferences = new EntityInsertionAdapter<UserPreferences>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `user_preferences` (`id`,`budgetPeriod`,`notificationsEnabled`,`reminderTime`,`darkModeEnabled`,`fontSize`,`primaryGoal`,`weeklyBudget`,`monthlyBudget`,`hasCompletedOnboarding`,`enableNotifications`,`notificationTime`,`theme`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserPreferences entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getBudgetPeriod() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getBudgetPeriod());
        }
        final int _tmp = entity.getNotificationsEnabled() ? 1 : 0;
        statement.bindLong(3, _tmp);
        if (entity.getReminderTime() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getReminderTime());
        }
        final int _tmp_1 = entity.getDarkModeEnabled() ? 1 : 0;
        statement.bindLong(5, _tmp_1);
        if (entity.getFontSize() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getFontSize());
        }
        if (entity.getPrimaryGoal() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getPrimaryGoal());
        }
        if (entity.getWeeklyBudget() == null) {
          statement.bindNull(8);
        } else {
          statement.bindDouble(8, entity.getWeeklyBudget());
        }
        if (entity.getMonthlyBudget() == null) {
          statement.bindNull(9);
        } else {
          statement.bindDouble(9, entity.getMonthlyBudget());
        }
        final int _tmp_2 = entity.getHasCompletedOnboarding() ? 1 : 0;
        statement.bindLong(10, _tmp_2);
        final int _tmp_3 = entity.getEnableNotifications() ? 1 : 0;
        statement.bindLong(11, _tmp_3);
        if (entity.getNotificationTime() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getNotificationTime());
        }
        if (entity.getTheme() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getTheme());
        }
      }
    };
    this.__updateAdapterOfUserPreferences = new EntityDeletionOrUpdateAdapter<UserPreferences>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `user_preferences` SET `id` = ?,`budgetPeriod` = ?,`notificationsEnabled` = ?,`reminderTime` = ?,`darkModeEnabled` = ?,`fontSize` = ?,`primaryGoal` = ?,`weeklyBudget` = ?,`monthlyBudget` = ?,`hasCompletedOnboarding` = ?,`enableNotifications` = ?,`notificationTime` = ?,`theme` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserPreferences entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getBudgetPeriod() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getBudgetPeriod());
        }
        final int _tmp = entity.getNotificationsEnabled() ? 1 : 0;
        statement.bindLong(3, _tmp);
        if (entity.getReminderTime() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getReminderTime());
        }
        final int _tmp_1 = entity.getDarkModeEnabled() ? 1 : 0;
        statement.bindLong(5, _tmp_1);
        if (entity.getFontSize() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getFontSize());
        }
        if (entity.getPrimaryGoal() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getPrimaryGoal());
        }
        if (entity.getWeeklyBudget() == null) {
          statement.bindNull(8);
        } else {
          statement.bindDouble(8, entity.getWeeklyBudget());
        }
        if (entity.getMonthlyBudget() == null) {
          statement.bindNull(9);
        } else {
          statement.bindDouble(9, entity.getMonthlyBudget());
        }
        final int _tmp_2 = entity.getHasCompletedOnboarding() ? 1 : 0;
        statement.bindLong(10, _tmp_2);
        final int _tmp_3 = entity.getEnableNotifications() ? 1 : 0;
        statement.bindLong(11, _tmp_3);
        if (entity.getNotificationTime() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getNotificationTime());
        }
        if (entity.getTheme() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getTheme());
        }
        statement.bindLong(14, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateBudgetPeriod = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET budgetPeriod = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateNotificationsEnabled = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET notificationsEnabled = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateDarkModeEnabled = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET darkModeEnabled = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateFontSize = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET fontSize = ? WHERE id = 1";
        return _query;
      }
    };
  }

  @Override
  public Object insertUserPreferences(final UserPreferences preferences,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfUserPreferences.insert(preferences);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUserPreferences(final UserPreferences preferences,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfUserPreferences.handle(preferences);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateBudgetPeriod(final String period,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateBudgetPeriod.acquire();
        int _argIndex = 1;
        if (period == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, period);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateBudgetPeriod.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateNotificationsEnabled(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateNotificationsEnabled.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateNotificationsEnabled.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateDarkModeEnabled(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateDarkModeEnabled.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateDarkModeEnabled.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateFontSize(final String fontSize,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateFontSize.acquire();
        int _argIndex = 1;
        if (fontSize == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, fontSize);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateFontSize.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<UserPreferences> getUserPreferences() {
    final String _sql = "SELECT * FROM user_preferences WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"user_preferences"}, new Callable<UserPreferences>() {
      @Override
      @Nullable
      public UserPreferences call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfNotificationsEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "notificationsEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfDarkModeEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "darkModeEnabled");
          final int _cursorIndexOfFontSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fontSize");
          final int _cursorIndexOfPrimaryGoal = CursorUtil.getColumnIndexOrThrow(_cursor, "primaryGoal");
          final int _cursorIndexOfWeeklyBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "weeklyBudget");
          final int _cursorIndexOfMonthlyBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "monthlyBudget");
          final int _cursorIndexOfHasCompletedOnboarding = CursorUtil.getColumnIndexOrThrow(_cursor, "hasCompletedOnboarding");
          final int _cursorIndexOfEnableNotifications = CursorUtil.getColumnIndexOrThrow(_cursor, "enableNotifications");
          final int _cursorIndexOfNotificationTime = CursorUtil.getColumnIndexOrThrow(_cursor, "notificationTime");
          final int _cursorIndexOfTheme = CursorUtil.getColumnIndexOrThrow(_cursor, "theme");
          final UserPreferences _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final boolean _tmpNotificationsEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfNotificationsEnabled);
            _tmpNotificationsEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final boolean _tmpDarkModeEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfDarkModeEnabled);
            _tmpDarkModeEnabled = _tmp_1 != 0;
            final String _tmpFontSize;
            if (_cursor.isNull(_cursorIndexOfFontSize)) {
              _tmpFontSize = null;
            } else {
              _tmpFontSize = _cursor.getString(_cursorIndexOfFontSize);
            }
            final String _tmpPrimaryGoal;
            if (_cursor.isNull(_cursorIndexOfPrimaryGoal)) {
              _tmpPrimaryGoal = null;
            } else {
              _tmpPrimaryGoal = _cursor.getString(_cursorIndexOfPrimaryGoal);
            }
            final Double _tmpWeeklyBudget;
            if (_cursor.isNull(_cursorIndexOfWeeklyBudget)) {
              _tmpWeeklyBudget = null;
            } else {
              _tmpWeeklyBudget = _cursor.getDouble(_cursorIndexOfWeeklyBudget);
            }
            final Double _tmpMonthlyBudget;
            if (_cursor.isNull(_cursorIndexOfMonthlyBudget)) {
              _tmpMonthlyBudget = null;
            } else {
              _tmpMonthlyBudget = _cursor.getDouble(_cursorIndexOfMonthlyBudget);
            }
            final boolean _tmpHasCompletedOnboarding;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfHasCompletedOnboarding);
            _tmpHasCompletedOnboarding = _tmp_2 != 0;
            final boolean _tmpEnableNotifications;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEnableNotifications);
            _tmpEnableNotifications = _tmp_3 != 0;
            final String _tmpNotificationTime;
            if (_cursor.isNull(_cursorIndexOfNotificationTime)) {
              _tmpNotificationTime = null;
            } else {
              _tmpNotificationTime = _cursor.getString(_cursorIndexOfNotificationTime);
            }
            final String _tmpTheme;
            if (_cursor.isNull(_cursorIndexOfTheme)) {
              _tmpTheme = null;
            } else {
              _tmpTheme = _cursor.getString(_cursorIndexOfTheme);
            }
            _result = new UserPreferences(_tmpId,_tmpBudgetPeriod,_tmpNotificationsEnabled,_tmpReminderTime,_tmpDarkModeEnabled,_tmpFontSize,_tmpPrimaryGoal,_tmpWeeklyBudget,_tmpMonthlyBudget,_tmpHasCompletedOnboarding,_tmpEnableNotifications,_tmpNotificationTime,_tmpTheme);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getUserPreferencesSync(final Continuation<? super UserPreferences> $completion) {
    final String _sql = "SELECT * FROM user_preferences WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserPreferences>() {
      @Override
      @Nullable
      public UserPreferences call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfNotificationsEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "notificationsEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfDarkModeEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "darkModeEnabled");
          final int _cursorIndexOfFontSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fontSize");
          final int _cursorIndexOfPrimaryGoal = CursorUtil.getColumnIndexOrThrow(_cursor, "primaryGoal");
          final int _cursorIndexOfWeeklyBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "weeklyBudget");
          final int _cursorIndexOfMonthlyBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "monthlyBudget");
          final int _cursorIndexOfHasCompletedOnboarding = CursorUtil.getColumnIndexOrThrow(_cursor, "hasCompletedOnboarding");
          final int _cursorIndexOfEnableNotifications = CursorUtil.getColumnIndexOrThrow(_cursor, "enableNotifications");
          final int _cursorIndexOfNotificationTime = CursorUtil.getColumnIndexOrThrow(_cursor, "notificationTime");
          final int _cursorIndexOfTheme = CursorUtil.getColumnIndexOrThrow(_cursor, "theme");
          final UserPreferences _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final boolean _tmpNotificationsEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfNotificationsEnabled);
            _tmpNotificationsEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final boolean _tmpDarkModeEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfDarkModeEnabled);
            _tmpDarkModeEnabled = _tmp_1 != 0;
            final String _tmpFontSize;
            if (_cursor.isNull(_cursorIndexOfFontSize)) {
              _tmpFontSize = null;
            } else {
              _tmpFontSize = _cursor.getString(_cursorIndexOfFontSize);
            }
            final String _tmpPrimaryGoal;
            if (_cursor.isNull(_cursorIndexOfPrimaryGoal)) {
              _tmpPrimaryGoal = null;
            } else {
              _tmpPrimaryGoal = _cursor.getString(_cursorIndexOfPrimaryGoal);
            }
            final Double _tmpWeeklyBudget;
            if (_cursor.isNull(_cursorIndexOfWeeklyBudget)) {
              _tmpWeeklyBudget = null;
            } else {
              _tmpWeeklyBudget = _cursor.getDouble(_cursorIndexOfWeeklyBudget);
            }
            final Double _tmpMonthlyBudget;
            if (_cursor.isNull(_cursorIndexOfMonthlyBudget)) {
              _tmpMonthlyBudget = null;
            } else {
              _tmpMonthlyBudget = _cursor.getDouble(_cursorIndexOfMonthlyBudget);
            }
            final boolean _tmpHasCompletedOnboarding;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfHasCompletedOnboarding);
            _tmpHasCompletedOnboarding = _tmp_2 != 0;
            final boolean _tmpEnableNotifications;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEnableNotifications);
            _tmpEnableNotifications = _tmp_3 != 0;
            final String _tmpNotificationTime;
            if (_cursor.isNull(_cursorIndexOfNotificationTime)) {
              _tmpNotificationTime = null;
            } else {
              _tmpNotificationTime = _cursor.getString(_cursorIndexOfNotificationTime);
            }
            final String _tmpTheme;
            if (_cursor.isNull(_cursorIndexOfTheme)) {
              _tmpTheme = null;
            } else {
              _tmpTheme = _cursor.getString(_cursorIndexOfTheme);
            }
            _result = new UserPreferences(_tmpId,_tmpBudgetPeriod,_tmpNotificationsEnabled,_tmpReminderTime,_tmpDarkModeEnabled,_tmpFontSize,_tmpPrimaryGoal,_tmpWeeklyBudget,_tmpMonthlyBudget,_tmpHasCompletedOnboarding,_tmpEnableNotifications,_tmpNotificationTime,_tmpTheme);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
