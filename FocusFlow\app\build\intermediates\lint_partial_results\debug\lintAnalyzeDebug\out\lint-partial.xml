<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="partial_results">
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.focusflow.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="19"
            column="12"
            startOffset="710"
            endLine="19"
            endColumn="24"
            endOffset="722"/>
        <location id="R.color.focus_orange"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="525"
            endLine="13"
            endColumn="31"
            endOffset="544"/>
        <location id="R.color.gray_100"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="22"
            column="12"
            startOffset="841"
            endLine="22"
            endColumn="27"
            endOffset="856"/>
        <location id="R.color.gray_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="887"
            endLine="23"
            endColumn="27"
            endOffset="902"/>
        <location id="R.color.gray_300"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="24"
            column="12"
            startOffset="933"
            endLine="24"
            endColumn="27"
            endOffset="948"/>
        <location id="R.color.gray_400"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="25"
            column="12"
            startOffset="979"
            endLine="25"
            endColumn="27"
            endOffset="994"/>
        <location id="R.color.gray_50"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="21"
            column="12"
            startOffset="796"
            endLine="21"
            endColumn="26"
            endOffset="810"/>
        <location id="R.color.gray_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="26"
            column="12"
            startOffset="1025"
            endLine="26"
            endColumn="27"
            endOffset="1040"/>
        <location id="R.color.gray_600"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="27"
            column="12"
            startOffset="1071"
            endLine="27"
            endColumn="27"
            endOffset="1086"/>
        <location id="R.color.gray_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="28"
            column="12"
            startOffset="1117"
            endLine="28"
            endColumn="27"
            endOffset="1132"/>
        <location id="R.color.gray_800"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="29"
            column="12"
            startOffset="1163"
            endLine="29"
            endColumn="27"
            endOffset="1178"/>
        <location id="R.color.gray_900"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="30"
            column="12"
            startOffset="1209"
            endLine="30"
            endColumn="27"
            endOffset="1224"/>
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="99"
            endLine="4"
            endColumn="29"
            endOffset="116"/>
        <location id="R.color.purple_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="147"
            endLine="5"
            endColumn="29"
            endOffset="164"/>
        <location id="R.color.purple_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="195"
            endLine="6"
            endColumn="29"
            endOffset="212"/>
        <location id="R.color.teal_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="243"
            endLine="7"
            endColumn="27"
            endOffset="258"/>
        <location id="R.color.teal_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="289"
            endLine="8"
            endColumn="27"
            endOffset="304"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="19"
            endColumn="10"
            endOffset="849"/>
        <location id="R.string.default_notification_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="561"
            endLine="9"
            endColumn="48"
            endOffset="596"/>
        <location id="R.string.default_notification_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="487"
            endLine="8"
            endColumn="46"
            endOffset="520"/>
        <location id="R.string.get_started"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="242"
            endLine="5"
            endColumn="31"
            endOffset="260"/>
        <location id="R.string.notification_channel_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="372"
            endLine="7"
            endColumn="52"
            endOffset="411"/>
        <location id="R.string.notification_channel_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="294"
            endLine="6"
            endColumn="45"
            endOffset="326"/>
        <location id="R.string.welcome_subtitle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="134"
            endLine="4"
            endColumn="36"
            endOffset="157"/>
        <location id="R.string.welcome_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="3"
            column="13"
            startOffset="71"
            endLine="3"
            endColumn="33"
            endOffset="91"/>
        <entry
            name="model"
            string="color[focus_blue(U),white(U),ic_launcher_background(U),purple_200(D),purple_500(D),purple_700(D),teal_200(D),teal_700(D),focus_blue_dark(U),focus_green(U),focus_orange(D),black(D),gray_50(D),gray_100(D),gray_200(D),gray_300(D),gray_400(D),gray_500(D),gray_600(D),gray_700(D),gray_800(D),gray_900(D)],drawable[ic_launcher_foreground(D)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U),welcome_title(D),welcome_subtitle(D),get_started(D),notification_channel_name(D),notification_channel_description(D),default_notification_title(D),default_notification_message(D)],style[Theme_FocusFlow(U),Theme_FocusFlow_NoActionBar(U),Theme_AppCompat_DayNight(R)],xml[data_extraction_rules(U),backup_rules(U),file_paths(U),network_security_config(U)];16^0^1,17^2,18^2,21^23^0^8^9^1,22^21;;;"/>
    </map>

</incidents>
