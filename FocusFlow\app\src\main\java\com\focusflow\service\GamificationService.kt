package com.focusflow.service

import com.focusflow.data.dao.AchievementDao
import com.focusflow.data.dao.UserStatsDao
import com.focusflow.data.dao.VirtualPetDao
import com.focusflow.data.model.Achievement
import com.focusflow.data.model.UserStats
import com.focusflow.data.model.VirtualPet
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GamificationService @Inject constructor(
    private val achievementDao: AchievementDao,
    private val userStatsDao: UserStatsDao,
    private val virtualPetDao: VirtualPetDao
) {
    
    suspend fun initializeGamification() {
        // Initialize user stats if not exists
        val userStats = userStatsDao.getUserStatsSync()
        if (userStats == null) {
            userStatsDao.insertUserStats(UserStats())
        }
        
        // Initialize virtual pet if not exists
        val virtualPet = virtualPetDao.getVirtualPetSync()
        if (virtualPet == null) {
            virtualPetDao.insertVirtualPet(VirtualPet())
        }
        
        // Initialize achievements if not exists
        initializeAchievements()
    }
    
    private suspend fun initializeAchievements() {
        val achievements = listOf(
            Achievement(
                type = "expense_logging",
                title = "First Step",
                description = "Log your first expense",
                iconEmoji = "🎯",
                pointsAwarded = 10,
                targetValue = 1
            ),
            Achievement(
                type = "expense_logging",
                title = "Getting Started",
                description = "Log 5 expenses",
                iconEmoji = "📝",
                pointsAwarded = 25,
                targetValue = 5
            ),
            Achievement(
                type = "expense_logging",
                title = "Tracking Master",
                description = "Log 25 expenses",
                iconEmoji = "📊",
                pointsAwarded = 100,
                targetValue = 25
            ),
            Achievement(
                type = "streak",
                title = "Consistency Champion",
                description = "Log expenses for 7 days in a row",
                iconEmoji = "🔥",
                pointsAwarded = 50,
                targetValue = 7
            ),
            Achievement(
                type = "streak",
                title = "Habit Hero",
                description = "Log expenses for 30 days in a row",
                iconEmoji = "⭐",
                pointsAwarded = 200,
                targetValue = 30
            ),
            Achievement(
                type = "budget_adherence",
                title = "Budget Buddy",
                description = "Stay within budget for a week",
                iconEmoji = "💰",
                pointsAwarded = 75,
                targetValue = 1
            ),
            Achievement(
                type = "debt_payment",
                title = "Debt Destroyer",
                description = "Make your first extra debt payment",
                iconEmoji = "💪",
                pointsAwarded = 100,
                targetValue = 1
            ),
            Achievement(
                type = "milestone",
                title = "Level Up!",
                description = "Reach level 5",
                iconEmoji = "🚀",
                pointsAwarded = 150,
                targetValue = 5
            )
        )
        
        achievements.forEach { achievement ->
            achievementDao.insertAchievement(achievement)
        }
    }
    
    suspend fun onExpenseLogged() {
        // Update stats
        userStatsDao.incrementExpensesLogged()
        val stats = userStatsDao.getUserStatsSync() ?: return
        
        // Update streak
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        val lastActivity = stats.lastActivityDate?.date
        
        val yesterday = today.minus(kotlinx.datetime.DatePeriod(days = 1))
        val newStreak = if (lastActivity == yesterday) {
            stats.expenseLoggingStreak + 1
        } else if (lastActivity == today) {
            stats.expenseLoggingStreak // Same day, don't increment
        } else {
            1 // Reset streak
        }
        
        userStatsDao.updateExpenseLoggingStreak(newStreak)
        
        // Check achievements
        checkExpenseLoggingAchievements(stats.totalExpensesLogged + 1)
        checkStreakAchievements(newStreak)
        
        // Feed virtual pet
        feedVirtualPet(5) // 5 experience points for logging expense
        
        // Award points
        awardPoints(5)
    }
    
    suspend fun onBudgetAdherence() {
        val stats = userStatsDao.getUserStatsSync() ?: return
        userStatsDao.updateBudgetAdherenceStreak(stats.budgetAdherenceStreak + 1)
        
        checkBudgetAchievements(stats.budgetAdherenceStreak + 1)
        feedVirtualPet(10)
        awardPoints(10)
    }
    
    suspend fun onDebtPayment(amount: Double) {
        userStatsDao.addDebtPaid(amount)
        
        checkDebtAchievements()
        feedVirtualPet(15)
        awardPoints(20)
    }
    
    private suspend fun checkExpenseLoggingAchievements(totalLogged: Int) {
        val milestones = listOf(1, 5, 25, 50, 100)
        milestones.forEach { milestone ->
            if (totalLogged >= milestone) {
                // Check if achievement exists and unlock it
                // This is simplified - in real implementation, you'd query for specific achievements
            }
        }
    }
    
    private suspend fun checkStreakAchievements(streak: Int) {
        val streakMilestones = listOf(7, 30, 60, 100)
        streakMilestones.forEach { milestone ->
            if (streak >= milestone) {
                // Unlock streak achievement
            }
        }
    }
    
    private suspend fun checkBudgetAchievements(streak: Int) {
        if (streak >= 1) {
            // Unlock budget adherence achievement
        }
    }
    
    private suspend fun checkDebtAchievements() {
        // Unlock debt payment achievement
    }
    
    private suspend fun awardPoints(points: Int) {
        userStatsDao.addPoints(points)
        
        // Check for level up
        val stats = userStatsDao.getUserStatsSync() ?: return
        val newLevel = calculateLevel(stats.totalPoints + points)
        
        if (newLevel > stats.currentLevel) {
            userStatsDao.updateUserStats(stats.copy(currentLevel = newLevel))
            // Trigger level up celebration
        }
    }
    
    private suspend fun feedVirtualPet(experience: Int) {
        val pet = virtualPetDao.getVirtualPetSync() ?: return
        
        val newExperience = pet.experience + experience
        val newLevel = calculatePetLevel(newExperience)
        val newHappiness = minOf(100, pet.happiness + 5)
        
        virtualPetDao.updateVirtualPet(
            pet.copy(
                experience = newExperience,
                level = newLevel,
                happiness = newHappiness,
                lastFed = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
            )
        )
    }
    
    private fun calculateLevel(totalPoints: Int): Int {
        return when {
            totalPoints < 100 -> 1
            totalPoints < 300 -> 2
            totalPoints < 600 -> 3
            totalPoints < 1000 -> 4
            totalPoints < 1500 -> 5
            totalPoints < 2100 -> 6
            totalPoints < 2800 -> 7
            totalPoints < 3600 -> 8
            totalPoints < 4500 -> 9
            else -> 10
        }
    }
    
    private fun calculatePetLevel(experience: Int): Int {
        return (experience / 100) + 1
    }
    
    suspend fun getMotivationalMessage(): String {
        val stats = userStatsDao.getUserStatsSync()
        val pet = virtualPetDao.getVirtualPetSync()
        
        return when {
            stats?.expenseLoggingStreak ?: 0 >= 7 -> "🔥 Amazing! You're on a ${stats?.expenseLoggingStreak}-day streak!"
            stats?.totalPoints ?: 0 >= 500 -> "⭐ You're doing fantastic! ${stats?.totalPoints} points earned!"
            pet?.happiness ?: 0 >= 90 -> "😸 ${pet?.name} is super happy with your progress!"
            else -> "💪 Keep going! Every small step counts!"
        }
    }
}
