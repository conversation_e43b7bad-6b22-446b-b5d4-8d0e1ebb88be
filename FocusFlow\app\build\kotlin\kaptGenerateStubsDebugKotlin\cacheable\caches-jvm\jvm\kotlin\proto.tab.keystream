"com/focusflow/FocusFlowApplicationcom/focusflow/MainActivitycom/focusflow/MainActivityKt'com/focusflow/data/dao/AIInteractionDao(com/focusflow/data/dao/BudgetCategoryDao$com/focusflow/data/dao/CreditCardDao!com/focusflow/data/dao/ExpenseDao"com/focusflow/data/dao/HabitLogDaocom/focusflow/data/dao/TaskDao)com/focusflow/data/dao/UserPreferencesDao&com/focusflow/data/database/Converters-com/focusflow/data/database/FocusFlowDatabase7com/focusflow/data/database/FocusFlowDatabase$Companion&com/focusflow/data/model/AIInteraction'com/focusflow/data/model/BudgetCategory#com/focusflow/data/model/CreditCard com/focusflow/data/model/Expense!com/focusflow/data/model/HabitLogcom/focusflow/data/model/Incomecom/focusflow/data/model/Task(com/focusflow/data/model/UserPreferences6com/focusflow/data/repository/BudgetCategoryRepository2com/focusflow/data/repository/CreditCardRepository/com/focusflow/data/repository/ExpenseRepository7com/focusflow/data/repository/UserPreferencesRepositorycom/focusflow/di/DatabaseModulecom/focusflow/navigation/Screen)com/focusflow/navigation/Screen$Dashboard(com/focusflow/navigation/Screen$Expenses$com/focusflow/navigation/Screen$Debt&com/focusflow/navigation/Screen$Budget&com/focusflow/navigation/Screen$Habits%com/focusflow/navigation/Screen$Tasks'com/focusflow/navigation/Screen$AICoach%com/focusflow/navigation/NavigationKt$com/focusflow/receiver/AlarmReceiver)com/focusflow/service/NotificationService3com/focusflow/service/NotificationService$Companion1com/focusflow/ui/components/BottomNavigationBarKt6com/focusflow/ui/components/ImpulseControlComponentsKt.com/focusflow/ui/onboarding/OnboardingActivity.com/focusflow/ui/onboarding/OnboardingScreenKt*com/focusflow/ui/screens/DashboardScreenKt%com/focusflow/ui/screens/DebtScreenKt'com/focusflow/ui/screens/OtherScreensKt-com/focusflow/ui/screens/PlaceholderScreensKtcom/focusflow/ui/theme/ColorKtcom/focusflow/ui/theme/ShapeKtcom/focusflow/ui/theme/ThemeKtcom/focusflow/ui/theme/TypeKt*com/focusflow/ui/viewmodel/BudgetViewModel(com/focusflow/ui/viewmodel/BudgetUiState2com/focusflow/ui/viewmodel/DefaultBudgetCategories-com/focusflow/ui/viewmodel/DashboardViewModel+com/focusflow/ui/viewmodel/DashboardUiState(com/focusflow/ui/viewmodel/DebtViewModel&com/focusflow/ui/viewmodel/DebtUiState,com/focusflow/ui/viewmodel/PayoffCalculation%com/focusflow/ui/viewmodel/PayoffStep)com/focusflow/ui/viewmodel/PayoffStrategy+com/focusflow/ui/viewmodel/ExpenseViewModel)com/focusflow/ui/viewmodel/ExpenseUiState,com/focusflow/ui/viewmodel/ExpenseCategories(com/focusflow/ui/viewmodel/MainViewModel&com/focusflow/ui/viewmodel/MainUiState.com/focusflow/ui/viewmodel/OnboardingViewModel,com/focusflow/ui/viewmodel/OnboardingUiState)com/focusflow/ui/viewmodel/OnboardingStep com/focusflow/utils/ErrorHandler&com/focusflow/utils/PerformanceMonitor#com/focusflow/utils/ValidationUtils!com/focusflow/utils/SecurityUtils&com/focusflow/utils/DataIntegrityUtils&com/focusflow/utils/AccessibilityUtils#com/focusflow/utils/ErrorHandlingKt.kotlin_module%com/focusflow/data/dao/AchievementDao#com/focusflow/data/dao/UserStatsDao$com/focusflow/data/dao/VirtualPetDao$com/focusflow/data/model/Achievement"com/focusflow/data/model/UserStats#com/focusflow/data/model/VirtualPet)com/focusflow/service/GamificationService+com/focusflow/ui/viewmodel/AICoachViewModel)com/focusflow/ui/viewmodel/AICoachUiState&com/focusflow/ui/viewmodel/ChatMessage,com/focusflow/data/model/StringListConverter                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          