7app/src/main/java/com/focusflow/FocusFlowApplication.kt/app/src/main/java/com/focusflow/MainActivity.kt<app/src/main/java/com/focusflow/data/dao/AIInteractionDao.kt=app/src/main/java/com/focusflow/data/dao/BudgetCategoryDao.kt9app/src/main/java/com/focusflow/data/dao/CreditCardDao.kt6app/src/main/java/com/focusflow/data/dao/ExpenseDao.kt;app/src/main/java/com/focusflow/data/dao/GamificationDao.kt7app/src/main/java/com/focusflow/data/dao/HabitLogDao.kt3app/src/main/java/com/focusflow/data/dao/TaskDao.kt>app/src/main/java/com/focusflow/data/dao/UserPreferencesDao.kt;app/src/main/java/com/focusflow/data/database/Converters.ktBapp/src/main/java/com/focusflow/data/database/FocusFlowDatabase.kt;app/src/main/java/com/focusflow/data/model/AIInteraction.kt9app/src/main/java/com/focusflow/data/model/Achievement.kt<app/src/main/java/com/focusflow/data/model/BudgetCategory.kt8app/src/main/java/com/focusflow/data/model/CreditCard.kt5app/src/main/java/com/focusflow/data/model/Expense.kt6app/src/main/java/com/focusflow/data/model/HabitLog.kt4app/src/main/java/com/focusflow/data/model/Income.kt2app/src/main/java/com/focusflow/data/model/Task.kt=app/src/main/java/com/focusflow/data/model/UserPreferences.ktKapp/src/main/java/com/focusflow/data/repository/BudgetCategoryRepository.ktGapp/src/main/java/com/focusflow/data/repository/CreditCardRepository.ktDapp/src/main/java/com/focusflow/data/repository/ExpenseRepository.ktLapp/src/main/java/com/focusflow/data/repository/UserPreferencesRepository.kt4app/src/main/java/com/focusflow/di/DatabaseModule.kt8app/src/main/java/com/focusflow/navigation/Navigation.kt9app/src/main/java/com/focusflow/receiver/AlarmReceiver.kt>app/src/main/java/com/focusflow/service/GamificationService.kt>app/src/main/java/com/focusflow/service/NotificationService.ktDapp/src/main/java/com/focusflow/ui/components/BottomNavigationBar.ktIapp/src/main/java/com/focusflow/ui/components/ImpulseControlComponents.ktCapp/src/main/java/com/focusflow/ui/onboarding/OnboardingActivity.ktAapp/src/main/java/com/focusflow/ui/onboarding/OnboardingScreen.kt=app/src/main/java/com/focusflow/ui/screens/DashboardScreen.kt8app/src/main/java/com/focusflow/ui/screens/DebtScreen.kt:app/src/main/java/com/focusflow/ui/screens/OtherScreens.kt@app/src/main/java/com/focusflow/ui/screens/PlaceholderScreens.kt1app/src/main/java/com/focusflow/ui/theme/Color.kt1app/src/main/java/com/focusflow/ui/theme/Shape.kt1app/src/main/java/com/focusflow/ui/theme/Theme.kt0app/src/main/java/com/focusflow/ui/theme/Type.kt@app/src/main/java/com/focusflow/ui/viewmodel/AICoachViewModel.kt?app/src/main/java/com/focusflow/ui/viewmodel/BudgetViewModel.ktBapp/src/main/java/com/focusflow/ui/viewmodel/DashboardViewModel.kt=app/src/main/java/com/focusflow/ui/viewmodel/DebtViewModel.kt@app/src/main/java/com/focusflow/ui/viewmodel/ExpenseViewModel.kt=app/src/main/java/com/focusflow/ui/viewmodel/MainViewModel.ktCapp/src/main/java/com/focusflow/ui/viewmodel/OnboardingViewModel.kt6app/src/main/java/com/focusflow/utils/ErrorHandling.kt[app/build/generated/source/kapt/debug/com/focusflow/ui/viewmodel/MainViewModel_Factory.java^app/build/generated/source/kapt/debug/com/focusflow/ui/viewmodel/ExpenseViewModel_Factory.javafapp/build/generated/source/kapt/debug/com/focusflow/di/DatabaseModule_ProvideVirtualPetDaoFactory.javagapp/build/generated/source/kapt/debug/com/focusflow/di/DatabaseModule_ProvideAchievementDaoFactory.javaeapp/build/generated/source/kapt/debug/com/focusflow/di/DatabaseModule_ProvideUserStatsDaoFactory.java                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                