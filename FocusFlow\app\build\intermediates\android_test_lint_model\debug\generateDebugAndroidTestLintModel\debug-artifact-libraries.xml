<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.navigation:navigation-common-ktx:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\61645e0b75a4ada9ae5c27dd38056a97\transformed\navigation-common-ktx-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-ktx:2.7.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\61645e0b75a4ada9ae5c27dd38056a97\transformed\navigation-common-ktx-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0939b5539bd9819785f5853c9457da19\transformed\navigation-common-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.7.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0939b5539bd9819785f5853c9457da19\transformed\navigation-common-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-ktx:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d95a08e99875f3f7a49b5fc5f12afb36\transformed\navigation-runtime-ktx-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-ktx:2.7.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d95a08e99875f3f7a49b5fc5f12afb36\transformed\navigation-runtime-ktx-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-navigation-compose:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\6d8c3d0d9894de3c4d59136e2d131b40\transformed\jetified-hilt-navigation-compose-1.1.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-navigation-compose:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\6d8c3d0d9894de3c4d59136e2d131b40\transformed\jetified-hilt-navigation-compose-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-navigation:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0715f2ded0511da1e5dc510e67116e36\transformed\jetified-hilt-navigation-1.1.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-navigation:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0715f2ded0511da1e5dc510e67116e36\transformed\jetified-hilt-navigation-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\5e956e8c4b9ff7ae956b1bc7abb3c361\transformed\navigation-runtime-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.7.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\5e956e8c4b9ff7ae956b1bc7abb3c361\transformed\navigation-runtime-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-testing:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\358073a3e2621fd1a9575d4efc432424\transformed\jetified-navigation-testing-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-testing:2.7.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\358073a3e2621fd1a9575d4efc432424\transformed\jetified-navigation-testing-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8fbf1e882f089cc53014c7bd78a857b7\transformed\jetified-navigation-compose-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose:2.7.5"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8fbf1e882f089cc53014c7bd78a857b7\transformed\jetified-navigation-compose-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:hilt-android-testing:2.48@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\5522e4b48d3a2d027996263319b6af8b\transformed\jetified-hilt-android-testing-2.48\jars\classes.jar"
      resolved="com.google.dagger:hilt-android-testing:2.48"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\5522e4b48d3a2d027996263319b6af8b\transformed\jetified-hilt-android-testing-2.48"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-work:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\076c8c54142036a60ab1c5a2fb077ebf\transformed\jetified-hilt-work-1.1.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-work:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\076c8c54142036a60ab1c5a2fb077ebf\transformed\jetified-hilt-work-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:hilt-android:2.48@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\4e47e64471bee267a0cae60354dcedc3\transformed\jetified-hilt-android-2.48\jars\classes.jar"
      resolved="com.google.dagger:hilt-android:2.48"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\4e47e64471bee267a0cae60354dcedc3\transformed\jetified-hilt-android-2.48"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.biometric:biometric:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0e1dc919567705f737931e90f7aead7b\transformed\biometric-1.1.0\jars\classes.jar"
      resolved="androidx.biometric:biometric:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0e1dc919567705f737931e90f7aead7b\transformed\biometric-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\6d9025e15716a4d8a0cfcec38b189854\transformed\fragment-1.5.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\6d9025e15716a4d8a0cfcec38b189854\transformed\fragment-1.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ef90906aa87718a52589e1b5b65a5bd2\transformed\jetified-activity-ktx-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ef90906aa87718a52589e1b5b65a5bd2\transformed\jetified-activity-ktx-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7c7c5a30567eb3e2d1c39db198a41f8c\transformed\jetified-activity-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7c7c5a30567eb3e2d1c39db198a41f8c\transformed\jetified-activity-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\83437aeff6196b298b04117d7acdaac7\transformed\jetified-activity-compose-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\83437aeff6196b298b04117d7acdaac7\transformed\jetified-activity-compose-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.espresso:espresso-core:3.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\9f4362e3392ee49b2ecfa2baac111ded\transformed\espresso-core-3.5.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-core:3.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\9f4362e3392ee49b2ecfa2baac111ded\transformed\espresso-core-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:core:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\603ad5805531611576d9e75907591e51\transformed\jetified-core-1.5.0\jars\classes.jar"
      resolved="androidx.test:core:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\603ad5805531611576d9e75907591e51\transformed\jetified-core-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\203b30e82298f565fda56b7f4627386d\transformed\jetified-core-ktx-1.12.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\203b30e82298f565fda56b7f4627386d\transformed\jetified-core-ktx-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1dcda92ead609fa3593df30260ad1f\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0c1dcda92ead609fa3593df30260ad1f\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0f8f42cc5fab3c055852f1f565dea58c\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0f8f42cc5fab3c055852f1f565dea58c\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\03571ca90648fce58779b285a5eb830f\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\03571ca90648fce58779b285a5eb830f\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\jars\classes.jar"
      resolved="androidx.core:core:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\251ce30526c21404d297545cb71e56a5\transformed\work-runtime-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\251ce30526c21404d297545cb71e56a5\transformed\work-runtime-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\5ee4d1b7da8139aa2b7e24f3988a77e8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\5ee4d1b7da8139aa2b7e24f3988a77e8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\49c98ad063b66e655f27a8b6594ecae1\transformed\lifecycle-livedata-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\49c98ad063b66e655f27a8b6594ecae1\transformed\lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\cc5c1bf140349cf20e5d2264009975da\transformed\lifecycle-livedata-core-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\cc5c1bf140349cf20e5d2264009975da\transformed\lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d57a180c9ec640813376e4de1defeced\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d57a180c9ec640813376e4de1defeced\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.7.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"
      provided="true"/>
  <library
      name="io.coil-kt:coil-compose:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\64d4268ba5ad50e481e710d7878d6f34\transformed\jetified-coil-compose-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-compose:2.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\64d4268ba5ad50e481e710d7878d6f34\transformed\jetified-coil-compose-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-compose-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f2d2dd3e22b83db8a9a8487a510d83fc\transformed\jetified-coil-compose-base-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-compose-base:2.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f2d2dd3e22b83db8a9a8487a510d83fc\transformed\jetified-coil-compose-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\60b4b2902d0c2d65398b02df805fd175\transformed\jetified-coil-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil:2.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\60b4b2902d0c2d65398b02df805fd175\transformed\jetified-coil-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0d5221fa0308d516e8dafbc8d9244b53\transformed\jetified-coil-base-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-base:2.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0d5221fa0308d516e8dafbc8d9244b53\transformed\jetified-coil-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1b3539420545eba470a6ac4d8c725246\transformed\lifecycle-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1b3539420545eba470a6ac4d8c725246\transformed\lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\99f4f840bdcbf1643133703fa17830bc\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\99f4f840bdcbf1643133703fa17830bc\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a787f09ed2bafcdbe799a8b9000e6968\transformed\lifecycle-viewmodel-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a787f09ed2bafcdbe799a8b9000e6968\transformed\lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.7.0\85334205d65cca70ed0109c3acbd29e22a2d9cb1\lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-testing:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b1034ed81baf6dc128109501807923be\transformed\jetified-lifecycle-runtime-testing-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-testing:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b1034ed81baf6dc128109501807923be\transformed\jetified-lifecycle-runtime-testing-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\39038db111d3a1fc6372d7eb1733a39c\transformed\jetified-lifecycle-runtime-compose-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\39038db111d3a1fc6372d7eb1733a39c\transformed\jetified-lifecycle-runtime-compose-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\451d4e76371e0a6b66b2361bd9a42e8e\transformed\jetified-lifecycle-runtime-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\451d4e76371e0a6b66b2361bd9a42e8e\transformed\jetified-lifecycle-runtime-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\2ea480ea8c9c66f94db9e300ecfb2ed3\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\2ea480ea8c9c66f94db9e300ecfb2ed3\transformed\jetified-lifecycle-viewmodel-compose-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\761136709342550333e594f75a6496b3\transformed\jetified-material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\761136709342550333e594f75a6496b3\transformed\jetified-material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a1db5251133fd46e803afb5485ed86e3\transformed\jetified-material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a1db5251133fd46e803afb5485ed86e3\transformed\jetified-material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1d9c4f44b8060fc88db876a4dce6a477\transformed\jetified-material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1d9c4f44b8060fc88db876a4dce6a477\transformed\jetified-material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c3a666876a1948e7171218a6769933a7\transformed\jetified-foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c3a666876a1948e7171218a6769933a7\transformed\jetified-foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\578305b89bd3ca9dee2bf1fe3893ba69\transformed\jetified-foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\578305b89bd3ca9dee2bf1fe3893ba69\transformed\jetified-foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\89a970527c344ee509b4d49c15e413fa\transformed\jetified-animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\89a970527c344ee509b4d49c15e413fa\transformed\jetified-animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f29c2260e78a2407d7cb9466b240d8e9\transformed\jetified-animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f29c2260e78a2407d7cb9466b240d8e9\transformed\jetified-animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\807b45bbd8e841787a37598a409008c5\transformed\jetified-ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\807b45bbd8e841787a37598a409008c5\transformed\jetified-ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\540f8de297042a7906895be5d56199a0\transformed\jetified-ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\540f8de297042a7906895be5d56199a0\transformed\jetified-ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\820fa46bc530437bf70aaa53ee0d33af\transformed\jetified-ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\820fa46bc530437bf70aaa53ee0d33af\transformed\jetified-ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1d8c3fe9269e92d0a3e6b0cf1ae33961\transformed\jetified-ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1d8c3fe9269e92d0a3e6b0cf1ae33961\transformed\jetified-ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3bce4def39d921a551e9760169d9935b\transformed\jetified-ui-test-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3bce4def39d921a551e9760169d9935b\transformed\jetified-ui-test-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8c3d8343e5aa681972c1538a36120bd6\transformed\jetified-ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8c3d8343e5aa681972c1538a36120bd6\transformed\jetified-ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a2678a7cfe444c1610586a94808387\transformed\jetified-ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a2678a7cfe444c1610586a94808387\transformed\jetified-ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a4db4a71831fe4752d07bd59b2538dc6\transformed\jetified-ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a4db4a71831fe4752d07bd59b2538dc6\transformed\jetified-ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1f170a70d96fcd26f910cf5fef71c6ab\transformed\jetified-ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1f170a70d96fcd26f910cf5fef71c6ab\transformed\jetified-ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-manifest:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\cbcef85fc27e89f72d399aad76d2f59a\transformed\jetified-ui-test-manifest-1.5.4\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-manifest:1.5.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\cbcef85fc27e89f72d399aad76d2f59a\transformed\jetified-ui-test-manifest-1.5.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-junit4-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\06d57ba4fd808958bb532800ab7bd1ce\transformed\jetified-ui-test-junit4-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-junit4-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\06d57ba4fd808958bb532800ab7bd1ce\transformed\jetified-ui-test-junit4-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.ext:junit:1.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0f89b8506ca3a7f0b19c3f53ddc161ea\transformed\jetified-junit-1.1.5\jars\classes.jar"
      resolved="androidx.test.ext:junit:1.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0f89b8506ca3a7f0b19c3f53ddc161ea\transformed\jetified-junit-1.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.20\2171532b615f8c10a288e9ef0ed34f37b5304d1d\kotlin-parcelize-runtime-1.9.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.20"/>
  <library
      name="androidx.room:room-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.6.1\ff1b9580850a9b7eef56554e356628d225785265\room-common-2.6.1.jar"
      resolved="androidx.room:room-common:2.6.1"
      provided="true"/>
  <library
      name="androidx.room:room-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\99626f5aff86c0f7ac0f9889669848a9\transformed\jetified-room-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\99626f5aff86c0f7ac0f9889669848a9\transformed\jetified-room-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.20\a77ec9b1d5d4819d5194fabb98c92787142a93db\kotlin-android-extensions-runtime-1.9.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.20"/>
  <library
      name="androidx.test:runner:1.5.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0f5e2af486387c04f4d011c86dc73332\transformed\runner-1.5.2\jars\classes.jar"
      resolved="androidx.test:runner:1.5.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0f5e2af486387c04f4d011c86dc73332\transformed\runner-1.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.services:storage:1.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\203512b06a072ee00222d210bfb6a7c6\transformed\jetified-storage-1.4.2\jars\classes.jar"
      resolved="androidx.test.services:storage:1.4.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\203512b06a072ee00222d210bfb6a7c6\transformed\jetified-storage-1.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:monitor:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\5c97e0c517cdb3ed86dd2cdff4ddfe52\transformed\monitor-1.6.1\jars\classes.jar"
      resolved="androidx.test:monitor:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\5c97e0c517cdb3ed86dd2cdff4ddfe52\transformed\monitor-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:annotation:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8642742e0ba3909730294a729cb64286\transformed\jetified-annotation-1.0.1\jars\classes.jar"
      resolved="androidx.test:annotation:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8642742e0ba3909730294a729cb64286\transformed\jetified-annotation-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\41588dac1ccb63a84af04a98d0131395\transformed\jetified-annotation-experimental-1.3.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\41588dac1ccb63a84af04a98d0131395\transformed\jetified-annotation-experimental-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\514bc2681f80738c044823bc0126083d\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\514bc2681f80738c044823bc0126083d\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\21a6a6d8b8d5c5045da238ec9be68151\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\21a6a6d8b8d5c5045da238ec9be68151\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-datetime-jvm\0.5.0\8882b30187d18d2dcb5e22587447485e6f42dfb3\kotlinx-datetime-jvm-0.5.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.5.0"
      provided="true"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d964e720205e6b8bad043f9899ef4633\transformed\jetified-runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d964e720205e6b8bad043f9899ef4633\transformed\jetified-runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2bb2f6242e20d4a17fc83490e591db\transformed\jetified-runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2bb2f6242e20d4a17fc83490e591db\transformed\jetified-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-test-jvm\1.7.3\89e34400f452dab68fbb3caa66d854c89aaafa07\kotlinx-coroutines-test-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"
      provided="true"/>
  <library
      name="com.google.accompanist:accompanist-permissions:0.32.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\6d0b1d640630166f7304ce3c4fa002e4\transformed\jetified-accompanist-permissions-0.32.0\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-permissions:0.32.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\6d0b1d640630166f7304ce3c4fa002e4\transformed\jetified-accompanist-permissions-0.32.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.github.vanpra.compose-material-dialogs:datetime:0.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\522eaa8dcceaba900a50e89ca4f51577\transformed\jetified-datetime-0.9.0\jars\classes.jar"
      resolved="io.github.vanpra.compose-material-dialogs:datetime:0.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\522eaa8dcceaba900a50e89ca4f51577\transformed\jetified-datetime-0.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.9.0\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\converter-gson-2.9.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.9.0"
      provided="true"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.9.0\d8fdfbd5da952141a665a403348b74538efc05ff\retrofit-2.9.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.9.0"
      provided="true"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"
      provided="true"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="androidx.security:security-crypto:1.1.0-alpha06@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\02139e6724a710870761a13124ce4c86\transformed\jetified-security-crypto-1.1.0-alpha06\jars\classes.jar"
      resolved="androidx.security:security-crypto:1.1.0-alpha06"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\02139e6724a710870761a13124ce4c86\transformed\jetified-security-crypto-1.1.0-alpha06"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ec984809a80c3ae349960313da4463\transformed\sqlite-framework-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ec984809a80c3ae349960313da4463\transformed\sqlite-framework-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c10c278cfe66148a5f3cede494cd7071\transformed\sqlite-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c10c278cfe66148a5f3cede494cd7071\transformed\sqlite-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\743fa516fc9d4a79c6cc0f0f997a2dfb\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\743fa516fc9d4a79c6cc0f0f997a2dfb\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\2a5f9de2b45882194175018fe0469237\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\2a5f9de2b45882194175018fe0469237\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-jvm:1.3.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.3.0\33d8b28dca2450e8656cfa23316eb656fb6f1299\collection-jvm-1.3.0.jar"
      resolved="androidx.collection:collection-jvm:1.3.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.7.0\920472d40adcdef5e18708976b3e314f9a636fcd\annotation-jvm-1.7.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.7.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.21\17ee3e873d439566c7d8354403b5f3d9744c4c9c\kotlin-stdlib-1.9.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.21"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-integration:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-integration\1.3\5de0c73fef18917cd85d0ab70bb23818685e4dfd\hamcrest-integration-1.3.jar"
      resolved="org.hamcrest:hamcrest-integration:1.3"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\259afdfe286f5c678cfd2e31a869ed8d\transformed\jetified-tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\259afdfe286f5c678cfd2e31a869ed8d\transformed\jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:hilt-core:2.48@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\hilt-core\2.48\b4568d616aefe08946cdeb1b7ad251d107a4a225\hilt-core-2.48.jar"
      resolved="com.google.dagger:hilt-core:2.48"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0d10faef53a64c4cc4f5661f98403ffe\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0d10faef53a64c4cc4f5661f98403ffe\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\6d5fef5a46b9d85b44f0113e93777b51\transformed\espresso-idling-resource-3.5.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-idling-resource:3.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\6d5fef5a46b9d85b44f0113e93777b51\transformed\espresso-idling-resource-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup:javawriter:2.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup\javawriter\2.1.1\67ff45d9ae02e583d0f9b3432a5ebbe05c30c966\javawriter-2.1.1.jar"
      resolved="com.squareup:javawriter:2.1.1"/>
  <library
      name="com.google.dagger:dagger:2.48@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\dagger\2.48\c4a5ecf0eb4df3a726179657f1b586290dc08d1b\dagger-2.48.jar"
      resolved="com.google.dagger:dagger:2.48"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.dagger:dagger-lint-aar:2.48@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\62384764d5d31fe34940ae42877f6f5e\transformed\jetified-dagger-lint-aar-2.48\jars\classes.jar"
      resolved="com.google.dagger:dagger-lint-aar:2.48"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\62384764d5d31fe34940ae42877f6f5e\transformed\jetified-dagger-lint-aar-2.48"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.multidex:multidex:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d2278a58e8030e23f686a6e842604158\transformed\multidex-2.0.1\jars\classes.jar"
      resolved="androidx.multidex:multidex:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d2278a58e8030e23f686a6e842604158\transformed\multidex-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.gson:gson:2.8.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.8.5\f645ed69d595b24d4cf8b3fbb64cc505bede8829\gson-2.8.5.jar"
      resolved="com.google.code.gson:gson:2.8.5"
      provided="true"/>
  <library
      name="androidx.hilt:hilt-common:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.hilt\hilt-common\1.1.0\9ca9c006cfce81d1435a0735fdab4b9e1166faa1\hilt-common-1.1.0.jar"
      resolved="androidx.hilt:hilt-common:1.1.0"
      provided="true"/>
  <library
      name="io.github.vanpra.compose-material-dialogs:core:0.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\031b9ae4944e79bcb281399ea8f4314a\transformed\jetified-core-0.9.0\jars\classes.jar"
      resolved="io.github.vanpra.compose-material-dialogs:core:0.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\031b9ae4944e79bcb281399ea8f4314a\transformed\jetified-core-0.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\319b71b6e6391419abc5b15b3b270788\transformed\jetified-ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\319b71b6e6391419abc5b15b3b270788\transformed\jetified-ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8cd39096ff5e90b2ee8900d321becb0c\transformed\jetified-customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8cd39096ff5e90b2ee8900d321becb0c\transformed\jetified-customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e3a416ab549a6e329100108b31b26951\transformed\jetified-autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e3a416ab549a6e329100108b31b26951\transformed\jetified-autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.3.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.3.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.3.0.jar"
      resolved="androidx.collection:collection-ktx:1.3.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\7231ef2d87ec9aca55378a7d77b83549\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\7231ef2d87ec9aca55378a7d77b83549\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
