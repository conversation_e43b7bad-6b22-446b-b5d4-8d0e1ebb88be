<variant
    name="debug"
    useSupportLibraryVectorDrawables="true"
    package="com.focusflow"
    minSdkVersion="24"
    targetSdkVersion="34"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    partialResultsDir="build\intermediates\android_test_lint_partial_results\debug\lintAnalyzeDebugAndroidTest\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\05f1af11feb90bf7d67306d315722be3\transformed\desugar_jdk_libs_configuration-2.0.4-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifests="src\androidTest\AndroidManifest.xml"
        javaDirectories="src\androidTest\java;src\androidTestDebug\java;src\androidTest\kotlin;src\androidTestDebug\kotlin"
        resDirectories="src\androidTest\res;src\androidTestDebug\res"
        assetsDirectories="src\androidTest\assets;src\androidTestDebug\assets"
        androidTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      type="INSTRUMENTATION_TEST"
      applicationId="com.focusflow.debug.test"
      generatedResourceFolders="build\generated\res\resValues\androidTest\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\05f1af11feb90bf7d67306d315722be3\transformed\desugar_jdk_libs_configuration-2.0.4-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
