package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.ExpenseRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ExpenseViewModel_Factory implements Factory<ExpenseViewModel> {
  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  public ExpenseViewModel_Factory(Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    this.expenseRepositoryProvider = expenseRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
  }

  @Override
  public ExpenseViewModel get() {
    return newInstance(expenseRepositoryProvider.get(), userPreferencesRepositoryProvider.get());
  }

  public static ExpenseViewModel_Factory create(
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    return new ExpenseViewModel_Factory(expenseRepositoryProvider, userPreferencesRepositoryProvider);
  }

  public static ExpenseViewModel newInstance(ExpenseRepository expenseRepository,
      UserPreferencesRepository userPreferencesRepository) {
    return new ExpenseViewModel(expenseRepository, userPreferencesRepository);
  }
}
