package com.focusflow.ui.onboarding

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.ui.viewmodel.OnboardingViewModel
import com.focusflow.ui.viewmodel.OnboardingStep

@Composable
fun OnboardingScreen(
    onOnboardingComplete: () -> Unit,
    viewModel: OnboardingViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        when (uiState.currentStep) {
            OnboardingStep.WELCOME -> WelcomeStep(
                onNext = { viewModel.nextStep() }
            )
            OnboardingStep.ADHD_FRIENDLY -> ADHDFriendlyStep(
                onNext = { viewModel.nextStep() }
            )
            OnboardingStep.GOALS_FINANCIAL -> FinancialGoalsStep(
                selectedGoals = uiState.selectedFinancialGoals,
                onGoalsChanged = viewModel::updateFinancialGoals,
                onNext = { viewModel.nextStep() }
            )
            OnboardingStep.GOALS_PERSONAL -> PersonalGoalsStep(
                selectedGoals = uiState.selectedPersonalGoals,
                onGoalsChanged = viewModel::updatePersonalGoals,
                onNext = { viewModel.nextStep() }
            )
            OnboardingStep.INCOME_SETUP -> IncomeSetupStep(
                monthlyIncome = uiState.monthlyIncome,
                onIncomeChanged = viewModel::updateMonthlyIncome,
                onNext = { viewModel.nextStep() },
                onSkip = { viewModel.nextStep() }
            )
            OnboardingStep.DEBT_SETUP -> DebtSetupStep(
                hasDebt = uiState.hasDebt,
                onDebtChanged = viewModel::updateHasDebt,
                onNext = { viewModel.nextStep() },
                onSkip = { viewModel.nextStep() }
            )
            OnboardingStep.BUDGET_SETUP -> BudgetSetupStep(
                weeklyBudget = uiState.weeklyBudget,
                onBudgetChanged = viewModel::updateWeeklyBudget,
                onNext = { viewModel.nextStep() },
                onSkip = { viewModel.nextStep() }
            )
            OnboardingStep.INCOME_SETUP -> IncomeSetupStep(
                monthlyIncome = uiState.monthlyIncome,
                onIncomeChanged = viewModel::updateMonthlyIncome,
                onNext = { viewModel.nextStep() },
                onSkip = { viewModel.nextStep() }
            )
            OnboardingStep.DEBT_SETUP -> DebtSetupStep(
                hasDebt = uiState.hasDebt,
                onDebtChanged = viewModel::updateHasDebt,
                onNext = { viewModel.nextStep() },
                onSkip = { viewModel.nextStep() }
            )
            OnboardingStep.BUDGET_SETUP -> BudgetSetupStep(
                weeklyBudget = uiState.weeklyBudget,
                onBudgetChanged = viewModel::updateWeeklyBudget,
                onNext = { viewModel.nextStep() },
                onSkip = { viewModel.nextStep() }
            )
            OnboardingStep.NOTIFICATION_SETUP -> NotificationSetupStep(
                enableNotifications = uiState.enableNotifications,
                notificationTime = uiState.notificationTime,
                onSettingsChanged = viewModel::updateNotificationSettings,
                onNext = { viewModel.nextStep() },
                onSkip = { viewModel.nextStep() }
            )
            OnboardingStep.COMPLETE -> CompleteStep(
                onFinish = {
                    viewModel.completeOnboarding()
                    onOnboardingComplete()
                }
            )
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // Progress indicator
        OnboardingProgressIndicator(
            currentStep = uiState.currentStep.ordinal,
            totalSteps = OnboardingStep.values().size - 1 // Exclude COMPLETE
        )
    }
}

@Composable
fun WelcomeStep(onNext: () -> Unit) {
    OnboardingStepLayout(
        icon =                Icons.Default.Face,
        title = "Welcome to FocusFlow!",
        description = "Your ADHD-friendly financial companion designed to make money management simple, visual, and stress-free.",
        primaryButtonText = "Get Started",
        onPrimaryClick = onNext
    )
}

@Composable
fun ADHDFriendlyStep(onNext: () -> Unit) {
    OnboardingStepLayout(
        icon = Icons.Default.Person,
        title = "Built for ADHD Minds",
        description = "• Visual progress indicators\n• Simple, clear interfaces\n• Gentle reminders, not overwhelming notifications\n• Quick actions to reduce friction",
        primaryButtonText = "That's Perfect!",
        onPrimaryClick = onNext
    )
}

@Composable
fun FinancialGoalsStep(
    selectedGoals: List<String>,
    onGoalsChanged: (List<String>) -> Unit,
    onNext: () -> Unit
) {
    val financialGoals = listOf(
        "Pay off debt",
        "Build emergency fund",
        "Save for a major purchase",
        "Improve credit score",
        "Reduce monthly expenses",
        "Increase savings",
        "Plan for retirement"
    )
    
    OnboardingStepLayout(
        icon = Icons.Default.Star,
        title = "What are your financial goals?",
        description = "Select the goals that matter most to you. We'll help you prioritize and track your progress.",
        content = {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(financialGoals) { goal ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                val updatedGoals = if (selectedGoals.contains(goal)) {
                                    selectedGoals - goal
                                } else {
                                    selectedGoals + goal
                                }
                                onGoalsChanged(updatedGoals)
                            },
                        elevation = if (selectedGoals.contains(goal)) 4.dp else 1.dp,
                        backgroundColor = if (selectedGoals.contains(goal)) {
                            MaterialTheme.colors.primary.copy(alpha = 0.1f)
                        } else {
                            MaterialTheme.colors.surface
                        }
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Checkbox(
                                checked = selectedGoals.contains(goal),
                                onCheckedChange = null
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = goal,
                                style = MaterialTheme.typography.body1
                            )
                        }
                    }
                }
            }
        },
        primaryButtonText = "Continue",
        onPrimaryClick = onNext
    )
}

@Composable
fun PersonalGoalsStep(
    selectedGoals: List<String>,
    onGoalsChanged: (List<String>) -> Unit,
    onNext: () -> Unit
) {
    val personalGoals = listOf(
        "Improve sleep habits",
        "Exercise regularly",
        "Take medication consistently",
        "Reduce stress",
        "Build daily routines",
        "Improve focus and productivity",
        "Better time management"
    )
    
    OnboardingStepLayout(
        icon = Icons.Default.FavoriteBorder,
        title = "What about personal wellness?",
        description = "Managing ADHD is about more than money. Select the areas where you'd like support.",
        content = {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(personalGoals) { goal ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                val updatedGoals = if (selectedGoals.contains(goal)) {
                                    selectedGoals - goal
                                } else {
                                    selectedGoals + goal
                                }
                                onGoalsChanged(updatedGoals)
                            },
                        elevation = if (selectedGoals.contains(goal)) 4.dp else 1.dp,
                        backgroundColor = if (selectedGoals.contains(goal)) {
                            MaterialTheme.colors.primary.copy(alpha = 0.1f)
                        } else {
                            MaterialTheme.colors.surface
                        }
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Checkbox(
                                checked = selectedGoals.contains(goal),
                                onCheckedChange = null
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = goal,
                                style = MaterialTheme.typography.body1
                            )
                        }
                    }
                }
            }
        },
        primaryButtonText = "Continue",
        onPrimaryClick = onNext
    )
}

@Composable
fun IncomeSetupStep(
    monthlyIncome: String,
    onIncomeChanged: (String) -> Unit,
    onNext: () -> Unit,
    onSkip: () -> Unit
) {
    OnboardingStepLayout(
        icon = Icons.Default.AccountBox,
        title = "What's your monthly income?",
        description = "This helps us suggest appropriate budget amounts and savings goals. Your information is private and secure.",
        content = {
            OutlinedTextField(
                value = monthlyIncome,
                onValueChange = onIncomeChanged,
                label = { Text("Monthly Income (after taxes)") },
                leadingIcon = { Text("$") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                placeholder = { Text("3,500") }
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Include all regular income sources",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
            )
        },
        primaryButtonText = "Continue",
        onPrimaryClick = onNext,
        secondaryButtonText = "Skip for Now",
        onSecondaryClick = onSkip
    )
}

@Composable
fun DebtSetupStep(
    hasDebt: Boolean?,
    onDebtChanged: (Boolean) -> Unit,
    onNext: () -> Unit,
    onSkip: () -> Unit
) {
    OnboardingStepLayout(
        icon = Icons.Default.Star,
        title = "Do you have credit card debt?",
        description = "We can help you create a payoff plan and track your progress. No judgment here - we're here to help!",
        content = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onDebtChanged(true) },
                    elevation = if (hasDebt == true) 4.dp else 1.dp,
                    backgroundColor = if (hasDebt == true) {
                        MaterialTheme.colors.primary.copy(alpha = 0.1f)
                    } else {
                        MaterialTheme.colors.surface
                    }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = hasDebt == true,
                            onClick = { onDebtChanged(true) }
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column {
                            Text(
                                text = "Yes, I have credit card debt",
                                style = MaterialTheme.typography.body1,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = "We'll help you create a payoff plan",
                                style = MaterialTheme.typography.caption,
                                color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                            )
                        }
                    }
                }
                
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onDebtChanged(false) },
                    elevation = if (hasDebt == false) 4.dp else 1.dp,
                    backgroundColor = if (hasDebt == false) {
                        MaterialTheme.colors.primary.copy(alpha = 0.1f)
                    } else {
                        MaterialTheme.colors.surface
                    }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = hasDebt == false,
                            onClick = { onDebtChanged(false) }
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column {
                            Text(
                                text = "No debt, or I pay it off monthly",
                                style = MaterialTheme.typography.body1,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = "Great! We'll focus on budgeting and saving",
                                style = MaterialTheme.typography.caption,
                                color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                            )
                        }
                    }
                }
            }
        },
        primaryButtonText = "Continue",
        onPrimaryClick = onNext,
        secondaryButtonText = "Skip for Now",
        onSecondaryClick = onSkip
    )
}



@Composable
fun NotificationSetupStep(
    enableNotifications: Boolean,
    notificationTime: String,
    onSettingsChanged: (Boolean, String) -> Unit,
    onNext: () -> Unit,
    onSkip: () -> Unit
) {
    OnboardingStepLayout(
        icon = Icons.Default.Notifications,
        title = "Gentle Reminders",
        description = "Get helpful, non-overwhelming notifications to stay on track with your financial goals.",
        content = {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = 2.dp,
                shape = RoundedCornerShape(8.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Enable Notifications",
                            style = MaterialTheme.typography.body1,
                            fontWeight = FontWeight.Medium
                        )
                        Switch(
                            checked = enableNotifications,
                            onCheckedChange = { onSettingsChanged(it, notificationTime) }
                        )
                    }
                    
                    if (enableNotifications) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "• Daily spending check-ins\n• Bill payment reminders\n• Weekly budget summaries",
                            style = MaterialTheme.typography.body2,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            }
        },
        primaryButtonText = if (enableNotifications) "Enable Notifications" else "Continue",
        onPrimaryClick = onNext,
        secondaryButtonText = "Skip",
        onSecondaryClick = onSkip
    )
}



@Composable
fun BudgetSetupStep(
    weeklyBudget: String,
    onBudgetChanged: (String) -> Unit,
    onNext: () -> Unit,
    onSkip: () -> Unit
) {
    OnboardingStepLayout(
        icon = Icons.Default.ShoppingCart,
        title = "Weekly Budget",
        description = "Set a weekly spending budget to help track your expenses. You can always adjust this later.",
        content = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                OutlinedTextField(
                    value = weeklyBudget,
                    onValueChange = onBudgetChanged,
                    label = { Text("Weekly Budget") },
                    placeholder = { Text("300") },
                    leadingIcon = { Text("$") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth()
                )

                Card(
                    backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "💡 ADHD-Friendly Tip",
                            style = MaterialTheme.typography.subtitle2,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "Weekly budgets are easier to manage than monthly ones. Start with a comfortable amount you can track daily.",
                            style = MaterialTheme.typography.body2
                        )
                    }
                }
            }
        },
        primaryButtonText = "Set Budget",
        onPrimaryClick = onNext,
        secondaryButtonText = "Skip for now",
        onSecondaryClick = onSkip
    )
}

@Composable
fun CompleteStep(onFinish: () -> Unit) {
    OnboardingStepLayout(
        icon = Icons.Default.CheckCircle,
        title = "You're All Set!",
        description = "FocusFlow is ready to help you manage your finances with confidence. Remember, every small step counts!",
        primaryButtonText = "Start Using FocusFlow",
        onPrimaryClick = onFinish
    )
}

@Composable
fun OnboardingStepLayout(
    icon: ImageVector,
    title: String,
    description: String,
    primaryButtonText: String,
    onPrimaryClick: () -> Unit,
    secondaryButtonText: String? = null,
    onSecondaryClick: (() -> Unit)? = null,
    content: @Composable (() -> Unit)? = null,
    primaryButtonEnabled: Boolean = true
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
    ) {
        Spacer(modifier = Modifier.height(32.dp))
        
        // Icon
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(80.dp),
            tint = MaterialTheme.colors.primary
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Title
        Text(
            text = title,
            style = MaterialTheme.typography.h4,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Description
        Text(
            text = description,
            style = MaterialTheme.typography.body1,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.8f),
            lineHeight = 24.sp
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Custom content
        content?.invoke()
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Buttons
        Button(
            onClick = onPrimaryClick,
            enabled = primaryButtonEnabled,
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp),
            shape = RoundedCornerShape(24.dp)
        ) {
            Text(
                text = primaryButtonText,
                style = MaterialTheme.typography.button,
                fontWeight = FontWeight.Medium
            )
        }
        
        if (secondaryButtonText != null && onSecondaryClick != null) {
            Spacer(modifier = Modifier.height(12.dp))
            TextButton(
                onClick = onSecondaryClick,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = secondaryButtonText,
                    style = MaterialTheme.typography.button
                )
            }
        }
    }
}

@Composable
fun OnboardingProgressIndicator(
    currentStep: Int,
    totalSteps: Int
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        repeat(totalSteps) { index ->
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = if (index <= currentStep) {
                            MaterialTheme.colors.primary
                        } else {
                            MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
                        },
                        shape = RoundedCornerShape(4.dp)
                    )
            )
            
            if (index < totalSteps - 1) {
                Spacer(modifier = Modifier.width(8.dp))
            }
        }
    }
}

