package com.focusflow.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.focusflow.data.dao.*
import com.focusflow.data.model.*

@Database(
    entities = [
        Expense::class,
        CreditCard::class,
        BudgetCategory::class,
        HabitLog::class,
        Task::class,
        AIInteraction::class,
        UserPreferences::class,
        Achievement::class,
        UserStats::class,
        VirtualPet::class
    ],
    version = 2,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class FocusFlowDatabase : RoomDatabase() {
    abstract fun expenseDao(): ExpenseDao
    abstract fun creditCardDao(): CreditCardDao
    abstract fun budgetCategoryDao(): BudgetCategoryDao
    abstract fun habitLogDao(): HabitLogDao
    abstract fun taskDao(): TaskDao
    abstract fun aiInteractionDao(): AIInteractionDao
    abstract fun userPreferencesDao(): UserPreferencesDao
    abstract fun achievementDao(): AchievementDao
    abstract fun userStatsDao(): UserStatsDao
    abstract fun virtualPetDao(): VirtualPetDao

    companion object {
        @Volatile
        private var INSTANCE: FocusFlowDatabase? = null

        fun getDatabase(context: Context): FocusFlowDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    FocusFlowDatabase::class.java,
                    "focusflow_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}

