<dependencies>
  <compile
      roots="androidx.hilt:hilt-navigation-compose:1.1.0@aar,androidx.hilt:hilt-navigation:1.1.0@aar,androidx.navigation:navigation-common:2.7.5@aar,androidx.navigation:navigation-runtime:2.7.5@aar,androidx.navigation:navigation-common-ktx:2.7.5@aar,androidx.navigation:navigation-runtime-ktx:2.7.5@aar,androidx.navigation:navigation-compose:2.7.5@aar,androidx.hilt:hilt-work:1.1.0@aar,com.google.dagger:hilt-android:2.48@aar,androidx.biometric:biometric:1.1.0@aar,androidx.fragment:fragment:1.5.1@aar,androidx.activity:activity:1.8.2@aar,androidx.activity:activity-compose:1.8.2@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.loader:loader:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.12.0@aar,androidx.core:core:1.12.0@aar,androidx.work:work-runtime-ktx:2.9.0@aar,androidx.work:work-runtime:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,io.coil-kt:coil-compose:2.5.0@aar,io.coil-kt:coil-compose-base:2.5.0@aar,io.coil-kt:coil:2.5.0@aar,io.coil-kt:coil-base:2.5.0@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-compose:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.core:core-ktx:1.12.0@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.20@jar,androidx.annotation:annotation-experimental:1.3.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.5.0@jar,androidx.compose.material:material-ripple-android:1.5.4@aar,androidx.compose.material:material-icons-core-android:1.5.4@aar,androidx.compose.material:material-android:1.5.4@aar,androidx.compose.animation:animation-core-android:1.5.4@aar,androidx.compose.foundation:foundation-layout-android:1.5.4@aar,androidx.compose.foundation:foundation-android:1.5.4@aar,androidx.compose.animation:animation-android:1.5.4@aar,androidx.compose.ui:ui-unit-android:1.5.4@aar,androidx.compose.ui:ui-text-android:1.5.4@aar,androidx.compose.ui:ui-geometry-android:1.5.4@aar,androidx.compose.ui:ui-graphics-android:1.5.4@aar,androidx.compose.ui:ui-android:1.5.4@aar,androidx.compose.ui:ui-tooling-preview-android:1.5.4@aar,androidx.compose.runtime:runtime-saveable-android:1.5.4@aar,androidx.compose.runtime:runtime-android:1.5.4@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.google.accompanist:accompanist-permissions:0.32.0@aar,io.github.vanpra.compose-material-dialogs:datetime:0.9.0@aar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.20@jar,androidx.security:security-crypto:1.1.0-alpha06@aar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection-jvm:1.3.0@jar,androidx.annotation:annotation-jvm:1.7.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.21@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:1.0@jar,androidx.startup:startup-runtime:1.1.1@aar,com.google.dagger:hilt-core:2.48@jar,com.google.dagger:dagger:2.48@jar,javax.inject:javax.inject:1@jar,com.google.dagger:dagger-lint-aar:2.48@aar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.code.gson:gson:2.8.9@jar,androidx.hilt:hilt-common:1.1.0@jar,io.github.vanpra.compose-material-dialogs:core:0.9.0@aar">
    <dependency
        name="androidx.hilt:hilt-navigation-compose:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation-compose"/>
    <dependency
        name="androidx.hilt:hilt-navigation:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation"/>
    <dependency
        name="androidx.navigation:navigation-common:2.7.5@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.7.5@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-compose:2.7.5@aar"
        simpleName="androidx.navigation:navigation-compose"/>
    <dependency
        name="androidx.hilt:hilt-work:1.1.0@aar"
        simpleName="androidx.hilt:hilt-work"/>
    <dependency
        name="com.google.dagger:hilt-android:2.48@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="androidx.biometric:biometric:1.1.0@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="androidx.fragment:fragment:1.5.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-compose:1.8.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.9.0@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.9.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="io.coil-kt:coil-compose:2.5.0@aar"
        simpleName="io.coil-kt:coil-compose"/>
    <dependency
        name="io.coil-kt:coil-compose-base:2.5.0@aar"
        simpleName="io.coil-kt:coil-compose-base"/>
    <dependency
        name="io.coil-kt:coil:2.5.0@aar"
        simpleName="io.coil-kt:coil"/>
    <dependency
        name="io.coil-kt:coil-base:2.5.0@aar"
        simpleName="io.coil-kt:coil-base"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.5.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-datetime-jvm"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.5.4@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.5.4@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.material:material-android:1.5.4@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.5.4@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.5.4@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.5.4@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.5.4@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.5.4@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.5.4@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.google.accompanist:accompanist-permissions:0.32.0@aar"
        simpleName="com.google.accompanist:accompanist-permissions"/>
    <dependency
        name="io.github.vanpra.compose-material-dialogs:datetime:0.9.0@aar"
        simpleName="io.github.vanpra.compose-material-dialogs:datetime"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.security:security-crypto:1.1.0-alpha06@aar"
        simpleName="androidx.security:security-crypto"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection-jvm:1.3.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.7.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="com.google.dagger:hilt-core:2.48@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.dagger:dagger:2.48@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.48@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.code.gson:gson:2.8.9@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="androidx.hilt:hilt-common:1.1.0@jar"
        simpleName="androidx.hilt:hilt-common"/>
    <dependency
        name="io.github.vanpra.compose-material-dialogs:core:0.9.0@aar"
        simpleName="io.github.vanpra.compose-material-dialogs:core"/>
  </compile>
  <package
      roots="io.github.vanpra.compose-material-dialogs:datetime:0.9.0@aar,androidx.biometric:biometric:1.1.0@aar,androidx.work:work-runtime-ktx:2.9.0@aar,androidx.hilt:hilt-work:1.1.0@aar,androidx.work:work-runtime:2.9.0@aar,androidx.hilt:hilt-navigation-compose:1.1.0@aar,androidx.hilt:hilt-navigation:1.1.0@aar,androidx.navigation:navigation-common:2.7.5@aar,androidx.navigation:navigation-runtime:2.7.5@aar,androidx.navigation:navigation-common-ktx:2.7.5@aar,androidx.navigation:navigation-runtime-ktx:2.7.5@aar,androidx.navigation:navigation-compose:2.7.5@aar,com.google.accompanist:accompanist-permissions:0.32.0@aar,com.google.dagger:hilt-android:2.48@aar,io.coil-kt:coil-compose:2.5.0@aar,io.coil-kt:coil-compose-base:2.5.0@aar,io.coil-kt:coil:2.5.0@aar,io.coil-kt:coil-base:2.5.0@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.fragment:fragment:1.5.1@aar,androidx.activity:activity:1.8.2@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.activity:activity-compose:1.8.2@aar,androidx.loader:loader:1.0.0@aar,com.google.accompanist:accompanist-pager:0.25.1@aar,io.github.vanpra.compose-material-dialogs:core:0.9.0@aar,androidx.compose.material:material-ripple-android:1.5.4@aar,androidx.compose.material:material-icons-core-android:1.5.4@aar,androidx.compose.material:material-android:1.5.4@aar,androidx.compose.animation:animation-core-android:1.5.4@aar,androidx.compose.animation:animation-android:1.5.4@aar,androidx.compose.foundation:foundation-layout-android:1.5.4@aar,dev.chrisbanes.snapper:snapper:0.2.2@aar,androidx.compose.foundation:foundation-android:1.5.4@aar,androidx.compose.ui:ui-unit-android:1.5.4@aar,androidx.compose.ui:ui-geometry-android:1.5.4@aar,androidx.compose.ui:ui-util-android:1.5.4@aar,androidx.compose.ui:ui-tooling-preview-android:1.5.4@aar,androidx.compose.ui:ui-graphics-android:1.5.4@aar,androidx.compose.ui:ui-text-android:1.5.4@aar,androidx.emoji2:emoji2-views-helper:1.4.0@aar,androidx.emoji2:emoji2:1.4.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-service:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-compose:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0@aar,com.google.accompanist:accompanist-drawablepainter:0.32.0@aar,androidx.compose.ui:ui-android:1.5.4@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.core:core:1.12.0@aar,androidx.core:core:1.12.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.12.0@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.20@jar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.5.0@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,androidx.compose.runtime:runtime-saveable-android:1.5.4@aar,androidx.compose.runtime:runtime-android:1.5.4@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.20@jar,androidx.annotation:annotation-experimental:1.3.0@aar,io.github.aakira:napier-android:1.4.1@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,androidx.security:security-crypto:1.1.0-alpha06@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.exifinterface:exifinterface:1.3.6@aar,androidx.collection:collection-ktx:1.3.0@jar,androidx.collection:collection-jvm:1.3.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.annotation:annotation-jvm:1.7.0@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.21@jar,org.jetbrains:annotations:23.0.0@jar,androidx.hilt:hilt-common:1.1.0@jar,com.google.dagger:hilt-core:2.48@jar,com.google.dagger:dagger:2.48@jar,com.google.dagger:dagger-lint-aar:2.48@aar,com.google.code.findbugs:jsr305:3.0.2@jar,javax.inject:javax.inject:1@jar,com.google.crypto.tink:tink-android:1.8.0@jar,com.google.code.gson:gson:2.8.9@jar,com.google.guava:listenablefuture:1.0@jar">
    <dependency
        name="io.github.vanpra.compose-material-dialogs:datetime:0.9.0@aar"
        simpleName="io.github.vanpra.compose-material-dialogs:datetime"/>
    <dependency
        name="androidx.biometric:biometric:1.1.0@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.9.0@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.hilt:hilt-work:1.1.0@aar"
        simpleName="androidx.hilt:hilt-work"/>
    <dependency
        name="androidx.work:work-runtime:2.9.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.hilt:hilt-navigation-compose:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation-compose"/>
    <dependency
        name="androidx.hilt:hilt-navigation:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation"/>
    <dependency
        name="androidx.navigation:navigation-common:2.7.5@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.7.5@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.7.5@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-compose:2.7.5@aar"
        simpleName="androidx.navigation:navigation-compose"/>
    <dependency
        name="com.google.accompanist:accompanist-permissions:0.32.0@aar"
        simpleName="com.google.accompanist:accompanist-permissions"/>
    <dependency
        name="com.google.dagger:hilt-android:2.48@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="io.coil-kt:coil-compose:2.5.0@aar"
        simpleName="io.coil-kt:coil-compose"/>
    <dependency
        name="io.coil-kt:coil-compose-base:2.5.0@aar"
        simpleName="io.coil-kt:coil-compose-base"/>
    <dependency
        name="io.coil-kt:coil:2.5.0@aar"
        simpleName="io.coil-kt:coil"/>
    <dependency
        name="io.coil-kt:coil-base:2.5.0@aar"
        simpleName="io.coil-kt:coil-base"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.fragment:fragment:1.5.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.8.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="com.google.accompanist:accompanist-pager:0.25.1@aar"
        simpleName="com.google.accompanist:accompanist-pager"/>
    <dependency
        name="io.github.vanpra.compose-material-dialogs:core:0.9.0@aar"
        simpleName="io.github.vanpra.compose-material-dialogs:core"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.5.4@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.5.4@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.material:material-android:1.5.4@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.5.4@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.5.4@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.5.4@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="dev.chrisbanes.snapper:snapper:0.2.2@aar"
        simpleName="dev.chrisbanes.snapper:snapper"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.5.4@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose"/>
    <dependency
        name="com.google.accompanist:accompanist-drawablepainter:0.32.0@aar"
        simpleName="com.google.accompanist:accompanist-drawablepainter"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.5.4@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.5.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-datetime-jvm"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.5.4@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.5.4@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="io.github.aakira:napier-android:1.4.1@aar"
        simpleName="io.github.aakira:napier-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.security:security-crypto:1.1.0-alpha06@aar"
        simpleName="androidx.security:security-crypto"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.collection:collection-ktx:1.3.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.3.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.7.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.hilt:hilt-common:1.1.0@jar"
        simpleName="androidx.hilt:hilt-common"/>
    <dependency
        name="com.google.dagger:hilt-core:2.48@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.dagger:dagger:2.48@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.48@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.crypto.tink:tink-android:1.8.0@jar"
        simpleName="com.google.crypto.tink:tink-android"/>
    <dependency
        name="com.google.code.gson:gson:2.8.9@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
